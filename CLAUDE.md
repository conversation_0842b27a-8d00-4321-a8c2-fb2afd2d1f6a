# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the Key Management Frontend (paotubao-front) - a React-based administration interface for managing API keys, models, organizations, and user accounts. It's part of the claude-code-proxy project ecosystem.

## Technology Stack

- **Framework**: React 19.0.0 with TypeScript 5.8
- **Build Tool**: Vite 6.3.5
- **Package Manager**: Bun
- **Styling**: Tailwind CSS v4 (integrated via Vite plugin)
- **UI Components**: Radix UI primitives + shadcn/ui components
- **State Management**: <PERSON><PERSON> (atomic state management)
- **Routing**: React Router v7
- **Forms**: React Hook Form with Zod validation
- **HTTP Client**: Axios with interceptors for auth
- **Data Tables**: TanStack Table v8
- **Charts**: Recharts for data visualization

## Development Commands

```bash
# Install dependencies
bun install

# Start development server
bun run dev

# Build for production
bun run build --mode=production

# Preview production build
bun run preview

# Run linting
bun run lint

# Deploy to production server
scp -r dist/ myhispread-page:/var/www/newplatform/
```

## Project Architecture

### Directory Structure
```
src/
├── api/                  # API service layer
│   ├── dash/            # Dashboard analytics APIs
│   ├── email/           # Email service integration
│   ├── key-management/  # Key CRUD operations
│   ├── model/           # AI model management
│   ├── organization/    # Organization management
│   ├── secret/          # Secret/token management
│   ├── user/            # User profile APIs
│   └── wallet/          # Wallet/balance functionality
├── app/                 # Page components (routes)
│   ├── dash/           # Main dashboard pages
│   └── login/          # Authentication pages
├── components/         # Reusable UI components
│   ├── ui/            # Base UI components (shadcn)
│   ├── table/         # Data table components
│   ├── modals/        # Modal dialog components
│   └── charts/        # Chart components
├── hooks/             # Custom React hooks
├── lib/               # Utilities and API client setup
├── router/            # Route configuration
├── state/             # Global state atoms (Jotai)
└── types/             # TypeScript type definitions
```

### Key Architectural Patterns

1. **API Layer Pattern**: Each feature has its own API module in `src/api/` with type-safe request/response interfaces
2. **Feature-Based Organization**: Pages in `src/app/dash/` mirror the API structure
3. **Component Composition**: UI built with shadcn/ui components on top of Radix UI primitives
4. **State Management**: Using Jotai atoms for global state, URL params for table state
5. **Type Safety**: Comprehensive TypeScript types in `src/types/` directory

## Important Configuration

### Path Aliases
- `@/*` maps to `./src/*` - use this for all imports

### Environment Variables
- `VITE_API_BASE_URL` - Backend API endpoint
- `VITE_LOGIN_URL` - Login page route
- `VITE_LOGIN_REDIRECT_URL` - Post-login redirect

### API Client Setup
The Axios client in `src/lib/api-client.ts` handles:
- Automatic token management from localStorage
- Request/response interceptors for auth
- Base URL configuration from environment

## Key Features & Modules

1. **Key Management** (`src/app/dash/key-management/`)
   - CRUD operations for API keys
   - Quota management
   - Usage tracking and analytics

2. **Model Management** (`src/app/dash/model/`)
   - AI model configuration
   - Model-specific settings and permissions

3. **Organization Management** (`src/app/dash/organization/`)
   - Multi-tenant organization support
   - User role management

4. **Dashboard Analytics** (`src/app/dash/home/<USER>
   - Usage statistics
   - Charts and visualizations

5. **Secret Management** (`src/app/dash/secret/`)
   - Secure token storage
   - Access control

## UI Component System

The project uses shadcn/ui components extensively. Key components:
- `Dialog`, `Sheet`, `Drawer` for modals
- `Table` with advanced features (sorting, filtering, pagination)
- `Form` with React Hook Form integration
- `Toast` notifications via Sonner
- Custom chart components with Recharts

## Code Conventions

1. **File Naming**: Use kebab-case for files and directories
2. **Component Naming**: PascalCase for React components
3. **Type Definitions**: Define interfaces in `src/types/` with descriptive names
4. **API Calls**: Use the centralized API client from `src/lib/api-client.ts`
5. **State Management**: Create Jotai atoms in `src/state/` for global state
6. **Form Validation**: Use Zod schemas with React Hook Form

## Notes

- No test framework is currently configured
- The UI includes Chinese language elements
- This is a management interface for the claude-code-proxy service
- Authentication uses token-based auth stored in localStorage