# 项目结构说明

## 总体架构

```
customer-platform/
├── public/                     # 静态资源目录
│   └── vite.svg               # 网站图标等静态文件
├── src/                       # 源代码目录
│   ├── api/                   # API 服务层
│   ├── app/                   # 页面组件 (路由页面)
│   ├── components/            # 可复用组件
│   ├── hooks/                 # 自定义 React Hooks
│   ├── lib/                   # 工具函数和配置
│   ├── router/                # 路由配置
│   ├── state/                 # 全局状态管理
│   ├── types/                 # TypeScript 类型定义
│   ├── assets/                # 静态资源 (图片、字体等)
│   ├── index.css              # 全局样式
│   ├── main.tsx               # 应用入口文件
│   └── vite-env.d.ts          # Vite 环境类型定义
├── components.json            # shadcn/ui 配置文件
├── eslint.config.js           # ESLint 配置
├── index.html                 # HTML 模板
├── package.json               # 项目依赖和脚本
├── tsconfig.json              # TypeScript 配置
├── tsconfig.app.json          # 应用 TypeScript 配置
├── tsconfig.node.json         # Node.js TypeScript 配置
├── vite.config.ts             # Vite 构建配置
└── README.md                  # 项目说明
```

## 详细目录结构

### 1. API 服务层 (`src/api/`)

按功能模块组织的 API 服务，每个模块包含 API 函数和类型定义。

```
src/api/
├── dash/                      # 仪表板相关 API
│   ├── dash-api.tsx          # 仪表板数据 API
│   └── dash-model.ts         # 仪表板数据类型
├── email/                     # 邮件服务 API
│   └── email-api.ts          # 邮件发送 API
├── key-management/            # 密钥管理 API
│   ├── common-key-api.ts     # 密钥 CRUD API
│   └── common-key-model.ts   # 密钥数据类型
├── llm-price/                 # LLM 价格管理 API
│   ├── llm-price-api.ts      # 价格 API
│   └── llm-price-model.ts    # 价格数据类型
├── model/                     # 模型管理 API
│   ├── model-api.ts          # 模型 API
│   └── model-model.ts        # 模型数据类型
├── organization/              # 组织管理 API
│   ├── organization-api.ts   # 组织 API
│   └── organization-modal.ts # 组织数据类型
├── organization-bind/         # 组织绑定 API
│   ├── organization-bind-api.ts
│   └── organization-bind-model.ts
├── secret/                    # 密钥管理 API
│   ├── secret-api.ts         # 密钥 API
│   └── secret-model.ts       # 密钥数据类型
├── system-model/              # 系统模型 API
│   ├── system-model-api.ts   # 系统模型 API
│   └── system-model-model.ts # 系统模型类型
├── user/                      # 用户管理 API
│   ├── user-api.ts           # 用户 API
│   └── user-model.ts         # 用户数据类型
├── user-wallet/               # 用户钱包 API
│   ├── user-wallet-api.ts    # 钱包 API
│   └── user-wallet-model.ts  # 钱包数据类型
└── wallet/                    # 钱包管理 API
    ├── wallet-api.tsx        # 钱包 API
    └── wallet-model.tsx      # 钱包数据类型
```

**API 模块规范:**
- 每个功能模块一个目录
- `*-api.ts` 文件包含 API 请求函数
- `*-model.ts` 文件包含 TypeScript 类型定义
- 使用统一的 API 客户端 (`@/lib/apiClient`)

### 2. 页面组件 (`src/app/`)

按路由结构组织的页面级组件。

```
src/app/
├── dash/                      # 主要仪表板页面
│   ├── home/                 # 首页仪表板
│   ├── key-management/       # 密钥管理页面
│   ├── model/                # 模型管理页面
│   ├── organization/         # 组织管理页面
│   ├── secret/               # 密钥管理页面
│   ├── user/                 # 用户管理页面
│   └── wallet/               # 钱包管理页面
└── login/                     # 登录相关页面
    ├── LoginPage.tsx         # 登录页面
    └── components/           # 登录相关组件
```

**页面组件规范:**
- 每个路由对应一个页面组件
- 页面组件负责数据获取和状态管理
- 使用 `PageLayout` 等布局组件
- 组合多个业务组件完成页面功能

### 3. 可复用组件 (`src/components/`)

按功能分类的可复用 UI 组件。

```
src/components/
├── ui/                        # 基础 UI 组件 (shadcn/ui)
│   ├── button/               # 按钮组件变体
│   │   ├── copy-button.tsx   # 复制按钮
│   │   ├── custom-button.tsx # 自定义按钮
│   │   └── new-button.tsx    # 新建按钮
│   ├── date/                 # 日期相关组件
│   │   ├── date-rage-picker.tsx # 日期范围选择器
│   │   └── month-picker.tsx  # 月份选择器
│   ├── select/               # 选择器组件
│   │   ├── data-table-column-filter.tsx # 表格列过滤器
│   │   └── select-with-input.tsx # 带输入的选择器
│   ├── new/                  # 新版本组件
│   │   └── dialog.tsx        # 新版对话框
│   ├── accordion.tsx         # 手风琴组件
│   ├── alert-dialog.tsx      # 警告对话框
│   ├── avatar.tsx            # 头像组件
│   ├── badge.tsx             # 徽章组件
│   ├── button.tsx            # 基础按钮
│   ├── calendar.tsx          # 日历组件
│   ├── card.tsx              # 卡片组件
│   ├── checkbox.tsx          # 复选框
│   ├── dialog.tsx            # 对话框
│   ├── dropdown-menu.tsx     # 下拉菜单
│   ├── form.tsx              # 表单组件
│   ├── input.tsx             # 输入框
│   ├── label.tsx             # 标签
│   ├── pagination.tsx        # 分页组件
│   ├── popover.tsx           # 弹出框
│   ├── select.tsx            # 选择器
│   ├── table.tsx             # 表格组件
│   ├── tabs.tsx              # 标签页
│   ├── textarea.tsx          # 文本域
│   ├── toast.tsx             # 提示组件
│   └── tooltip.tsx           # 工具提示
├── auth/                      # 认证相关组件
│   ├── AuthGuard.tsx         # 路由守卫
│   ├── LoginForm.tsx         # 登录表单
│   └── PermissionCheck.tsx   # 权限检查
├── charts/                    # 图表组件
│   ├── BarChart.tsx          # 柱状图
│   ├── LineChart.tsx         # 折线图
│   └── PieChart.tsx          # 饼图
├── command/                   # 命令面板组件
│   └── CommandPalette.tsx    # 命令面板
├── icons/                     # 图标组件
│   ├── Logo.tsx              # 网站 Logo
│   └── CustomIcons.tsx       # 自定义图标
├── layout/                    # 布局组件
│   ├── AppLayout.tsx         # 应用主布局
│   ├── PageHeader.tsx        # 页面头部
│   ├── Sidebar.tsx           # 侧边栏
│   └── Navigation.tsx        # 导航组件
├── modals/                    # 模态框组件
│   ├── index.ts              # 模态框提供者
│   ├── ConfirmModal.tsx      # 确认对话框
│   └── CreateUserModal.tsx   # 创建用户模态框
├── select/                    # 选择器组件
│   ├── UserSelector.tsx      # 用户选择器
│   └── ModelSelector.tsx     # 模型选择器
├── table/                     # 表格相关组件
│   ├── DataTable.tsx         # 数据表格
│   ├── TableColumns.tsx      # 表格列定义
│   └── TableActions.tsx      # 表格操作
└── toolbar/                   # 工具栏组件
    └── PageToolbar.tsx       # 页面工具栏
```

**组件分类说明:**
- `ui/`: 基础 UI 组件，基于 shadcn/ui 和 Radix UI
- `auth/`: 认证和权限相关组件
- `charts/`: 数据可视化组件
- `layout/`: 页面布局组件
- `modals/`: 模态框和对话框组件
- `table/`: 数据表格相关组件

### 4. 自定义 Hooks (`src/hooks/`)

可复用的 React Hooks。

```
src/hooks/
├── use-export-file.tsx        # 文件导出 Hook
├── use-file-upload.ts         # 文件上传 Hook
├── use-mobile.ts              # 移动端检测 Hook
├── use-pagination.ts          # 分页状态 Hook
├── use-slider-with-input.ts   # 滑块输入 Hook
├── use-toast.ts               # 提示消息 Hook
└── use-url-param.tsx          # URL 参数 Hook
```

**Hooks 规范:**
- 以 `use` 开头命名
- 封装可复用的状态逻辑
- 提供清晰的 TypeScript 类型

### 5. 工具函数和配置 (`src/lib/`)

通用工具函数和配置文件。

```
src/lib/
├── apiClient.ts               # API 客户端配置
└── utils.ts                   # 通用工具函数
```

**工具函数规范:**
- `apiClient.ts`: Axios 配置和拦截器
- `utils.ts`: 通用工具函数 (如 `cn` 类名合并函数)

### 6. 路由配置 (`src/router/`)

应用路由配置。

```
src/router/
└── self-route.tsx             # 路由定义和配置
```

**路由规范:**
- 使用 React Router v7
- 支持懒加载
- 集成认证守卫

### 7. 状态管理 (`src/state/`)

全局状态管理 (Jotai atoms)。

```
src/state/
├── table-state.ts             # 表格状态管理
└── user-state.ts              # 用户状态管理
```

**状态管理规范:**
- 使用 Jotai 原子化状态管理
- 按功能模块组织状态
- 提供派生状态和异步操作

### 8. 类型定义 (`src/types/`)

TypeScript 类型定义。

```
src/types/
├── key-binding.ts             # 密钥绑定类型
└── index.ts                   # 通用类型定义
```

**类型定义规范:**
- 全局通用类型放在 `index.ts`
- 功能特定类型单独文件
- 与 API 模型保持一致

## 配置文件说明

### 1. 构建配置

- **`vite.config.ts`**: Vite 构建配置
  - React 插件配置
  - Tailwind CSS 插件
  - 路径别名设置 (`@` -> `./src`)

- **`tsconfig.json`**: TypeScript 配置
  - 项目引用配置
  - 路径映射设置

### 2. 代码质量

- **`eslint.config.js`**: ESLint 配置
  - TypeScript 支持
  - React Hooks 规则
  - React Refresh 规则

### 3. UI 组件

- **`components.json`**: shadcn/ui 配置
  - 组件样式 (new-york)
  - Tailwind 配置
  - 路径别名设置

### 4. 包管理

- **`package.json`**: 项目依赖和脚本
  - 开发依赖: TypeScript, ESLint, Vite
  - 生产依赖: React, UI 组件库, 状态管理等

## 开发工作流

### 1. 新功能开发流程

1. **API 层**: 在 `src/api/` 创建对应模块
2. **类型定义**: 定义 TypeScript 接口
3. **状态管理**: 在 `src/state/` 添加相关状态
4. **组件开发**: 创建可复用组件
5. **页面组合**: 在 `src/app/` 创建页面组件
6. **路由配置**: 更新路由配置

### 2. 组件开发流程

1. **基础组件**: 扩展 `src/components/ui/`
2. **业务组件**: 在 `src/components/` 对应分类下创建
3. **Hooks 抽取**: 将复杂逻辑抽取到 `src/hooks/`
4. **类型定义**: 确保完整的 TypeScript 支持

### 3. 样式开发流程

1. **Tailwind 优先**: 使用 Tailwind CSS 类名
2. **组件变体**: 使用 `class-variance-authority`
3. **主题定制**: 通过 CSS 变量定制主题
4. **响应式设计**: 使用 Tailwind 响应式前缀

## 最佳实践

### 1. 文件命名

- **组件文件**: PascalCase (如 `UserProfile.tsx`)
- **工具文件**: kebab-case (如 `api-client.ts`)
- **目录名**: kebab-case (如 `key-management/`)

### 2. 导入规范

- 使用路径别名 `@/` 
- 按类型分组导入 (React -> 第三方 -> 内部)
- 类型导入使用 `import type`

### 3. 组件设计

- 单一职责原则
- 清晰的 Props 接口
- 适当的错误边界
- 性能优化 (memo, useMemo, useCallback)

### 4. 状态管理

- 优先使用本地状态
- 全局状态使用 Jotai
- URL 状态用于表格等可分享状态

---

这个项目结构设计支持大型应用的开发和维护，通过清晰的分层和模块化设计，确保代码的可读性和可维护性。
