# 开发指南

## 开发环境搭建

### 1. 环境要求
- **JDK**: OpenJDK 21 或 Oracle JDK 21
- **IDE**: IntelliJ IDEA 2023.3+ (推荐)
- **数据库**: PostgreSQL 12+
- **缓存**: Redis 6+
- **构建工具**: Gradle 8.14.3+

### 2. 本地环境配置

#### 数据库配置
```bash
# 安装PostgreSQL
brew install postgresql

# 启动PostgreSQL服务
brew services start postgresql

# 创建数据库
createdb toms

# 创建用户(可选)
psql -d toms -c "CREATE USER postgres WITH PASSWORD 'root';"
psql -d toms -c "GRANT ALL PRIVILEGES ON DATABASE toms TO postgres;"
```

#### Redis配置
```bash
# 安装Redis
brew install redis

# 启动Redis服务
brew services start redis

# 验证Redis连接
redis-cli ping
```

### 3. 项目导入
```bash
# 克隆项目
git clone <repository-url>
cd lux-customer-platform-backend

# 导入IDEA
# File -> Open -> 选择项目根目录

# Gradle同步
./gradlew build
```

## 代码规范

### 1. 包结构规范
```
io.cliveyou.luxcustomerplatformbackend
├── application/          # 应用服务层
├── common/              # 公共组件
│   ├── enums/          # 枚举类
│   ├── exception/      # 异常类
│   └── utils/          # 工具类
├── config/             # 配置类
├── domain/             # 领域模型
├── facade/             # 接口层
└── infrastructure/     # 基础设施层
    └── repository/     # 数据访问层
```

### 2. 命名规范

#### 类命名
- **Controller**: 以`Controller`结尾，如`UserController`
- **Service**: 以`Service`结尾，如`UserLoginApplicationService`
- **Repository**: 以`Repository`结尾，如`CustomerPlatformAccountRepository`
- **Entity**: 使用业务名词，如`CustomerPlatformAccount`
- **DTO**: 以`Request`或`Response`结尾

#### 方法命名
- **查询方法**: `find`、`get`、`query`开头
- **保存方法**: `save`、`create`、`add`开头
- **更新方法**: `update`、`modify`开头
- **删除方法**: `delete`、`remove`开头

#### 变量命名
- 使用驼峰命名法
- 布尔变量以`is`、`has`、`can`开头
- 常量使用大写字母和下划线

### 3. 注解使用规范

#### Spring注解
```kotlin
@RestController
@RequestMapping("/users")
class UserController

@Service
class UserLoginApplicationService

@Repository
interface CustomerPlatformAccountRepository

@Component
class ApiSignatureInterceptor
```

#### JPA注解
```kotlin
@Entity(name = "customer_platform_accounts")
@Table(name = "customer_platform_accounts")
class CustomerPlatformAccount : AbstractBaseEntity() {
    
    @Id
    @Column(name = "id", nullable = false)
    var id: Long = nextId()
    
    @Column(name = "email", nullable = false, columnDefinition = "text")
    var email: String = ""
}
```

## 开发流程

### 1. 功能开发流程
1. **需求分析**: 理解业务需求，设计API接口
2. **数据建模**: 设计实体类和数据库表结构
3. **接口设计**: 定义Controller接口和DTO
4. **业务实现**: 实现Application Service业务逻辑
5. **数据访问**: 实现Repository数据访问层
6. **测试验证**: 编写单元测试和集成测试

### 2. 分层开发原则

#### Facade层开发
```kotlin
@RestController
@RequestMapping("/users")
class UserController(
    private val userLoginApplicationService: UserLoginApplicationService,
) {
    @PostMapping("/login")
    fun login(@RequestBody request: UserLoginRequest): String {
        return userLoginApplicationService.login(request)
    }
}
```

#### Application层开发
```kotlin
@Service
class UserLoginApplicationService(
    private val userRepository: UserRepository,
    private val userRedisRepository: UserRedisRepository,
) {
    @Transactional
    fun login(request: UserLoginRequest): String {
        // 1. 验证用户凭证
        val user = userRepository.findByAccountAndPassword(request.account, request.password)
            ?: throw IllegalArgumentException("账号密码不正确")
        
        // 2. 生成Token
        val token = Jwt.createToken(user.id)
        
        // 3. 保存会话
        userRedisRepository.saveUser(user, token)
        
        return token
    }
}
```

#### Domain层开发
```kotlin
@Entity(name = "customer_platform_accounts")
class CustomerPlatformAccount : AbstractBaseEntity() {
    // 实体属性定义
    
    // 业务方法
    fun isActive(): Boolean = status == BaseStatus.ENABLED
    
    fun updateLastLoginTime() {
        lastLoginAt = System.currentTimeMillis()
    }
}
```

#### Infrastructure层开发
```kotlin
@Repository
interface CustomerPlatformAccountRepository : JpaRepository<CustomerPlatformAccount, Long> {
    fun findByEmailAndBizId(email: String, bizId: Long): CustomerPlatformAccount?
}
```

### 3. 异常处理规范

#### 自定义异常
```kotlin
class BusinessException(message: String) : RuntimeException(message)
class ValidationException(message: String) : RuntimeException(message)
```

#### 全局异常处理
```kotlin
@RestControllerAdvice
class GlobalExceptionHandler {
    
    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(ex: IllegalArgumentException): ResponseEntity<ErrorResult> {
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, ex.message ?: ""))
    }
}
```

## 配置管理

### 1. 多环境配置
```yaml
# application.yaml - 基础配置
spring:
  application:
    name: toms
  profiles:
    active: local

# application-local.yaml - 本地环境
spring:
  datasource:
    url: *************************************

# application-prod.yaml - 生产环境
spring:
  datasource:
    url: *************************************
```

### 2. 配置类开发
```kotlin
@Configuration
class WebConfig : WebMvcConfigurer {
    
    override fun addInterceptors(registry: InterceptorRegistry) {
        registry.addInterceptor(apiSignatureInterceptor)
            .addPathPatterns("/**")
            .excludePathPatterns("/login", "/logout")
    }
}
```

## 测试规范

### 1. 单元测试
```kotlin
@ExtendWith(MockitoExtension::class)
class UserLoginApplicationServiceTest {
    
    @Mock
    private lateinit var userRepository: UserRepository
    
    @Mock
    private lateinit var userRedisRepository: UserRedisRepository
    
    @InjectMocks
    private lateinit var userLoginApplicationService: UserLoginApplicationService
    
    @Test
    fun `should login successfully with valid credentials`() {
        // Given
        val request = UserLoginRequest("<EMAIL>", "password")
        val user = CustomerPlatformAccount().apply {
            id = 1L
            email = "<EMAIL>"
        }
        
        `when`(userRepository.findByAccountAndPassword(any(), any())).thenReturn(user)
        
        // When
        val result = userLoginApplicationService.login(request)
        
        // Then
        assertThat(result).isNotNull()
        verify(userRedisRepository).saveUser(user, result)
    }
}
```

### 2. 集成测试
```kotlin
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = ["spring.profiles.active=test"])
class UserControllerIntegrationTest {
    
    @Autowired
    private lateinit var testRestTemplate: TestRestTemplate
    
    @Test
    fun `should login successfully`() {
        // Given
        val request = UserLoginRequest("<EMAIL>", "password")
        
        // When
        val response = testRestTemplate.postForEntity("/api/users/login", request, String::class.java)
        
        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.OK)
        assertThat(response.body).isEqualTo("登录成功")
    }
}
```

## 调试技巧

### 1. 日志配置
```yaml
logging:
  level:
    io.cliveyou.luxcustomerplatformbackend: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
```

### 2. 开发工具
- **Spring Boot DevTools**: 热重载
- **H2 Console**: 数据库调试
- **Actuator**: 应用监控

### 3. 常用调试命令
```bash
# 查看应用状态
curl http://localhost:8080/actuator/health

# 查看配置信息
curl http://localhost:8080/actuator/configprops

# 查看Bean信息
curl http://localhost:8080/actuator/beans
```

## 代码质量

### 1. 代码检查工具
- **Ktlint**: Kotlin代码格式检查
- **Detekt**: Kotlin静态代码分析
- **SonarQube**: 代码质量分析

### 2. Git提交规范
```bash
# 提交格式
<type>(<scope>): <subject>

# 示例
feat(user): add user login functionality
fix(auth): fix JWT token validation issue
docs(api): update API documentation
```

### 3. 代码审查清单
- [ ] 代码符合命名规范
- [ ] 异常处理完善
- [ ] 日志记录合理
- [ ] 单元测试覆盖
- [ ] 性能考虑
- [ ] 安全检查
