# 前端开发规范文档

## 项目概述

本项目是基于 React + TypeScript 的客户平台前端应用，采用现代化的技术栈和开发模式，为用户提供密钥管理、模型管理、组织管理等功能的管理界面。

## 技术栈

### 核心技术
- **框架**: React 19.0.0 + TypeScript 5.8
- **构建工具**: Vite 6.3.5
- **包管理器**: Bun
- **路由**: React Router v7
- **状态管理**: Jo<PERSON> (原子化状态管理)

### UI 和样式
- **样式框架**: Tailwind CSS v4 (通过 Vite 插件集成)
- **UI 组件库**: Radix UI + shadcn/ui
- **图标库**: Lucide React + @lobehub/icons
- **动画**: Framer Motion

### 表单和数据
- **表单管理**: React Hook Form + Zod 验证
- **HTTP 客户端**: Axios (带拦截器)
- **数据表格**: TanStack Table v8
- **图表**: Recharts

### 开发工具
- **代码检查**: ESLint + TypeScript ESLint
- **通知**: Sonner
- **拖拽**: @dnd-kit
- **国际化**: react-i18next + i18next-browser-languagedetector

## 项目结构

```
customer-platform/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 服务层
│   │   ├── dash/         # 仪表板相关 API
│   │   ├── email/        # 邮件服务 API
│   │   ├── key-management/ # 密钥管理 API
│   │   ├── model/        # 模型管理 API
│   │   ├── organization/ # 组织管理 API
│   │   ├── secret/       # 密钥管理 API
│   │   ├── user/         # 用户相关 API
│   │   └── wallet/       # 钱包相关 API
│   ├── app/              # 页面组件 (路由页面)
│   │   ├── dash/        # 主要仪表板页面
│   │   └── login/       # 登录相关页面
│   ├── components/       # 可复用 UI 组件
│   │   ├── ui/          # 基础 UI 组件 (shadcn)
│   │   ├── table/       # 数据表格组件
│   │   ├── modals/      # 模态框组件
│   │   ├── charts/      # 图表组件
│   │   ├── auth/        # 认证相关组件
│   │   ├── layout/      # 布局组件
│   │   └── icons/       # 图标组件
│   ├── hooks/            # 自定义 React Hooks
│   ├── lib/              # 工具函数和 API 客户端
│   │   └── i18n.ts      # 国际化配置
│   ├── locales/          # 国际化翻译文件
│   │   ├── en.json      # 英文翻译
│   │   ├── zh.json      # 中文翻译
│   │   └── vi.json      # 越南语翻译
│   ├── router/           # 路由配置
│   ├── state/            # 全局状态 (Jotai atoms)
│   ├── types/            # TypeScript 类型定义
│   └── assets/           # 静态资源
├── components.json       # shadcn/ui 配置
├── vite.config.ts       # Vite 配置
├── tsconfig.json        # TypeScript 配置
├── eslint.config.js     # ESLint 配置
└── package.json         # 项目依赖
```

## 开发环境配置

### 环境变量
在项目根目录创建 `.env` 文件：

```env
VITE_API_BASE_URL=http://localhost:8080
VITE_LOGIN_URL=/login
VITE_LOGIN_REDIRECT_URL=/dash
```

### 开发命令

```bash
# 安装依赖
bun install

# 启动开发服务器
bun run dev

# 构建生产版本
bun run build --mode=production

# 预览生产构建
bun run preview

# 代码检查
bun run lint

# 部署到生产服务器
scp -r dist/ myhispread-page:/var/www/newplatform/
```

## 编码规范

### 文件命名规范

1. **文件和目录**: 使用 kebab-case
   ```
   user-profile.tsx
   key-management/
   api-client.ts
   ```

2. **React 组件**: 使用 PascalCase
   ```tsx
   export const UserProfile = () => { ... }
   export const KeyManagementTable = () => { ... }
   ```

3. **类型定义**: 使用 PascalCase，接口以 I 开头或使用 type
   ```tsx
   interface UserProfile { ... }
   type ApiResponse<T> = { ... }
   ```

### 导入规范

1. **路径别名**: 统一使用 `@/` 别名
   ```tsx
   import { apiClient } from '@/lib/apiClient'
   import { UserProfile } from '@/components/user/UserProfile'
   import type { User } from '@/types/user'
   ```

2. **导入顺序**:
   ```tsx
   // 1. React 相关
   import React from 'react'
   import { useState, useEffect } from 'react'
   
   // 2. 第三方库
   import { useForm } from 'react-hook-form'
   import { zodResolver } from '@hookform/resolvers/zod'
   
   // 3. 内部模块 (按层级排序)
   import { apiClient } from '@/lib/apiClient'
   import { Button } from '@/components/ui/button'
   import type { User } from '@/types/user'
   ```

### 组件开发规范

1. **函数组件**: 优先使用函数组件和 Hooks
   ```tsx
   interface UserCardProps {
     user: User
     onEdit?: (user: User) => void
   }
   
   export const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
     const [isLoading, setIsLoading] = useState(false)
     
     return (
       <div className="p-4 border rounded-lg">
         {/* 组件内容 */}
       </div>
     )
   }
   ```

2. **Props 类型定义**: 为每个组件定义清晰的 Props 接口
   ```tsx
   interface TableProps<T> {
     data: T[]
     columns: ColumnDef<T>[]
     loading?: boolean
     onRowClick?: (row: T) => void
   }
   ```

3. **事件处理**: 使用明确的事件处理函数命名
   ```tsx
   const handleUserEdit = (user: User) => { ... }
   const handleFormSubmit = (data: FormData) => { ... }
   const handleTableRowClick = (row: TableRow) => { ... }
   ```

### API 层规范

1. **API 模块结构**: 每个功能模块包含 API 和 Model 文件
   ```
   src/api/user/
   ├── user-api.ts    # API 请求函数
   └── user-model.ts  # 类型定义
   ```

2. **API 函数定义**:
   ```tsx
   // user-api.ts
   export const userApi = {
     login: async (data: LoginRequest): Promise<UserWithToken> => {
       return await apiClient.post({
         url: '/api/user/auth/password/login',
         data
       })
     },
     
     profile: async (): Promise<UserProfile> => {
       return await apiClient.get({
         url: '/api/user/auth/profile'
       })
     }
   }
   ```

3. **类型定义**:
   ```tsx
   // user-model.ts
   export interface User {
     id: string
     email: string
     name: string
     createdAt: string
   }
   
   export interface LoginRequest {
     email: string
     password: string
   }
   
   export interface UserWithToken extends User {
     token: string
   }
   ```

### 状态管理规范

1. **Jotai Atoms**: 在 `src/state/` 目录下定义全局状态
   ```tsx
   // src/state/user-state.ts
   import { atom } from 'jotai'
   import type { User } from '@/types/user'
   
   export const userAtom = atom<User | null>(null)
   export const isAuthenticatedAtom = atom((get) => get(userAtom) !== null)
   ```

2. **本地状态**: 使用 React Hooks 管理组件内部状态
   ```tsx
   const [isLoading, setIsLoading] = useState(false)
   const [formData, setFormData] = useState<FormData>({})
   ```

### 表单处理规范

1. **使用 React Hook Form + Zod**:
   ```tsx
   import { useForm } from 'react-hook-form'
   import { zodResolver } from '@hookform/resolvers/zod'
   import { z } from 'zod'
   
   const formSchema = z.object({
     email: z.string().email('请输入有效的邮箱地址'),
     password: z.string().min(6, '密码至少6位')
   })
   
   type FormData = z.infer<typeof formSchema>
   
   export const LoginForm = () => {
     const form = useForm<FormData>({
       resolver: zodResolver(formSchema),
       defaultValues: {
         email: '',
         password: ''
       }
     })
     
     const onSubmit = async (data: FormData) => {
       // 处理表单提交
     }
     
     return (
       <Form {...form}>
         {/* 表单内容 */}
       </Form>
     )
   }
   ```

### 样式规范

1. **Tailwind CSS**: 优先使用 Tailwind 类名
   ```tsx
   <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
     <h2 className="text-lg font-semibold text-gray-900">标题</h2>
     <Button variant="outline" size="sm">操作</Button>
   </div>
   ```

2. **响应式设计**: 使用 Tailwind 响应式前缀
   ```tsx
   <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
     {/* 内容 */}
   </div>
   ```

3. **组件变体**: 使用 class-variance-authority 管理组件变体
   ```tsx
   import { cva } from 'class-variance-authority'
   
   const buttonVariants = cva(
     'inline-flex items-center justify-center rounded-md text-sm font-medium',
     {
       variants: {
         variant: {
           default: 'bg-primary text-primary-foreground hover:bg-primary/90',
           outline: 'border border-input hover:bg-accent'
         },
         size: {
           default: 'h-10 px-4 py-2',
           sm: 'h-9 rounded-md px-3'
         }
       }
     }
   )
   ```

## 错误处理规范

1. **API 错误处理**: 在 API 客户端统一处理
   ```tsx
   // 在 apiClient.ts 中的响应拦截器
   axiosInstance.interceptors.response.use(
     (response) => response.data,
     (error) => {
       if (error.status === 401) {
         // 处理认证失败
         localStorage.removeItem(userTokenKey)
         window.location.href = import.meta.env.VITE_LOGIN_URL
       }
       return Promise.reject(new Error(error.response?.data?.message || '系统错误'))
     }
   )
   ```

2. **组件错误边界**: 使用 React Error Boundary
   ```tsx
   import { ErrorBoundary } from 'react-error-boundary'
   
   const ErrorFallback = ({ error }: { error: Error }) => (
     <div className="p-4 text-center">
       <h2>出现错误</h2>
       <p>{error.message}</p>
     </div>
   )
   
   <ErrorBoundary FallbackComponent={ErrorFallback}>
     <YourComponent />
   </ErrorBoundary>
   ```

## 性能优化规范

1. **懒加载**: 对路由组件使用懒加载
   ```tsx
   import { lazy } from 'react'
   
   const Dashboard = lazy(() => import('@/app/dash/Dashboard'))
   ```

2. **memo 优化**: 对纯组件使用 React.memo
   ```tsx
   export const UserCard = React.memo<UserCardProps>(({ user }) => {
     return <div>{user.name}</div>
   })
   ```

3. **useMemo 和 useCallback**: 优化计算和函数引用
   ```tsx
   const expensiveValue = useMemo(() => {
     return computeExpensiveValue(data)
   }, [data])
   
   const handleClick = useCallback((id: string) => {
     onItemClick(id)
   }, [onItemClick])
   ```

## 测试规范

目前项目暂未配置测试框架，建议后续添加：
- **单元测试**: Jest + React Testing Library
- **E2E 测试**: Playwright 或 Cypress
- **类型检查**: TypeScript 严格模式

## 部署规范

1. **构建命令**: `bun run build --mode=production`
2. **部署脚本**: `scp -r dist/ myhispread-page:/var/www/newplatform/`
3. **环境变量**: 确保生产环境变量正确配置

## 代码审查清单

- [ ] 代码符合命名规范
- [ ] 组件有明确的 TypeScript 类型定义
- [ ] 使用了正确的路径别名 `@/`
- [ ] API 调用有错误处理
- [ ] 表单使用了 Zod 验证
- [ ] 样式使用 Tailwind CSS
- [ ] 组件可复用性良好
- [ ] 性能优化合理
- [ ] 无 ESLint 错误

## 常见问题和解决方案

1. **路径别名不生效**: 检查 `vite.config.ts` 和 `tsconfig.json` 配置
2. **Tailwind 样式不生效**: 确认 `@tailwindcss/vite` 插件正确配置
3. **API 请求失败**: 检查环境变量和 API 客户端配置
4. **类型错误**: 确保所有 API 响应都有对应的类型定义
5. **翻译不显示**: 检查翻译键名是否正确，确保在所有语言文件中都存在

## 国际化 (i18n)

项目支持多语言国际化功能，详细信息请参考 [国际化实现指南](./INTERNATIONALIZATION_GUIDE.md)。

### 快速使用

```typescript
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('dashboard.home')}</h1>
      <button>{t('common.save')}</button>
    </div>
  );
}
```

### 支持的语言

- **英文 (en)**: 默认语言
- **中文 (zh)**: 简体中文
- **越南语 (vi)**: Tiếng Việt

### 语言切换

使用 `LanguageSwitcher` 组件实现语言切换：

```typescript
import LanguageSwitcher from '@/components/ui/language-switcher';

<LanguageSwitcher variant="ghost" size="sm" showText={false} />
```

---

本规范文档将随着项目发展持续更新，请开发团队严格遵循以确保代码质量和项目可维护性。
