# 权限控制指南

## 概述

本指南介绍如何在 SpecBuilder 中方便地注入用户权限控制，确保用户只能查询自己有权限的数据。

## 权限控制原理

### 用户上下文
系统通过 `UserContextHolder` 管理当前登录用户的信息：

```kotlin
data class UserContext(
    val id: Long,              // 用户ID
    val customerId: Long?,     // 客户ID（用于数据权限控制）
    val bizId: Long,           // 业务租户ID
    val token: String,
    val name: String,
    // ...
)
```

### 权限控制字段
- **customerId**: 客户数据权限，用户只能查看自己的数据
- **bizId**: 租户数据权限，用于多租户数据隔离

## 使用方式

### 1. 注解方式（最推荐）

#### 基本权限控制注解
```kotlin
@RequireUserPermission  // 自动添加 customerId 权限控制
data class SubOrderPageRequest(
    val orderNo: String? = null,
    val customerOrderNo: String? = null,
) : SpecRequest<SubOrder>, OmsPageRequest() {

    override fun convertToSpec(): Specification<SubOrder> {
        // 只需要写业务查询条件，权限控制自动添加
        return SpecBuilder.of<SubOrder>()
            .like(SubOrder::orderNo, orderNo)
            .like(SubOrder::customerOrderNo, customerOrderNo)
            .build()
    }
}

// 在 Controller 中使用
@PostMapping("/orders/search")
fun searchOrders(@RequestBody request: SubOrderPageRequest): Page<SubOrder> {
    // 使用带权限控制的查询
    val spec = request.convertToSpecWithPermissions()
    return orderRepository.findAll(spec, request.toPageable())
}
```

#### 自定义字段名和可空支持
```kotlin
@RequireUserPermission(customerIdField = "ownerId", nullable = true)
data class ProductPageRequest(
    val name: String? = null
) : SpecRequest<Product>, OmsPageRequest() {
    // 权限控制会自动应用到 Product::ownerId 字段
}

@RequireBizPermission(bizIdField = "tenantId")
data class UserPageRequest(
    val name: String? = null
) : SpecRequest<User>, OmsPageRequest() {
    // 权限控制会自动应用到 User::tenantId 字段
}
```

#### 复合权限控制
```kotlin
@RequirePermissions(
    types = [PermissionType.USER, PermissionType.BIZ],
    logic = PermissionLogic.AND
)
data class OrderPageRequest(
    val status: String? = null
) : SpecRequest<Order>, OmsPageRequest() {
    // 同时需要用户权限和租户权限
}

@RequirePermissions(
    types = [PermissionType.USER, PermissionType.PUBLIC],
    logic = PermissionLogic.OR
)
data class ArticlePageRequest(
    val title: String? = null
) : SpecRequest<Article>, OmsPageRequest() {
    // 用户自己的文章 OR 公共文章
}
```

### 2. 自动权限注入（编程方式）

#### 基本用法
```kotlin
// 自动注入当前用户的 customerId 权限控制
override fun convertToSpec(): Specification<SubOrder> {
    return SpecBuilder.withUserPermission(SubOrder::customerId)
        .like(SubOrder::orderNo, orderNo)
        .like(SubOrder::customerOrderNo, customerOrderNo)
        .between(SubOrder::createdAt, start, end)
        .build()
}
```

#### 支持可空字段
```kotlin
// 如果 customerId 字段是可空的
override fun convertToSpec(): Specification<Order> {
    return SpecBuilder.withUserPermissionNullable(Order::customerId)
        .equal(Order::status, status)
        .build()
}
```

### 2. 手动权限控制

#### 在构建过程中添加权限
```kotlin
override fun convertToSpec(): Specification<SubOrder> {
    return SpecBuilder.of<SubOrder>()
        .withCurrentUserPermission(SubOrder::customerId)  // 手动添加权限控制
        .like(SubOrder::orderNo, orderNo)
        .like(SubOrder::customerOrderNo, customerOrderNo)
        .build()
}
```

#### 多重权限控制
```kotlin
override fun convertToSpec(): Specification<SubOrder> {
    return SpecBuilder.of<SubOrder>()
        .withCurrentUserPermission(SubOrder::customerId)  // 客户权限
        .withBizPermission(SubOrder::bizId)               // 租户权限
        .like(SubOrder::orderNo, orderNo)
        .build()
}
```

### 3. 条件权限控制

#### 根据角色决定是否添加权限
```kotlin
override fun convertToSpec(): Specification<SubOrder> {
    val builder = SpecBuilder.of<SubOrder>()
        .like(SubOrder::orderNo, orderNo)
        .like(SubOrder::customerOrderNo, customerOrderNo)
    
    // 只有普通用户需要权限控制，管理员可以查看所有数据
    val currentUser = UserContextHolder.user
    if (currentUser?.role != UserRole.ADMIN) {
        builder.withCurrentUserPermission(SubOrder::customerId)
    }
    
    return builder.build()
}
```

## 实际应用示例

### 订单查询权限控制
```kotlin
data class SubOrderPageRequest(
    val orderNo: String? = null,
    val customerOrderNo: String? = null,
    val start: Long? = null,
    val end: Long? = null,
) : SpecRequest<SubOrder>, OmsPageRequest() {
    
    override fun convertToSpec(): Specification<SubOrder> {
        // 用户只能查询自己的订单
        return SpecBuilder.withUserPermission(SubOrder::customerId)
            .like(SubOrder::orderNo, orderNo)
            .like(SubOrder::customerOrderNo, customerOrderNo)
            .between(SubOrder::createdAt, start, end)
            .build()
    }
}
```

### 用户管理权限控制
```kotlin
data class UserPageRequest(
    val name: String? = null,
    val email: String? = null,
) : SpecRequest<CustomerPlatformAccount>, OmsPageRequest() {
    
    override fun convertToSpec(): Specification<CustomerPlatformAccount> {
        // 租户隔离：用户只能查看同一租户下的用户
        return SpecBuilder.of<CustomerPlatformAccount>()
            .withBizPermission(CustomerPlatformAccount::bizId)
            .like(CustomerPlatformAccount::accountName, name)
            .like(CustomerPlatformAccount::email, email)
            .build()
    }
}
```

### 复杂权限场景
```kotlin
data class ProductPageRequest(
    val name: String? = null,
    val category: String? = null,
    val includePublic: Boolean = false,  // 是否包含公共产品
) : SpecRequest<Product>, OmsPageRequest() {
    
    override fun convertToSpec(): Specification<Product> {
        val builder = SpecBuilder.of<Product>()
            .like(Product::name, name)
            .like(Product::category, category)
        
        // 权限控制：用户数据 OR 公共数据
        val currentUser = UserContextHolder.user
        if (currentUser?.customerId != null) {
            if (includePublic) {
                // 用户自己的产品 OR 公共产品
                val userProducts = SpecBuilder.of<Product>()
                    .equal(Product::customerId, currentUser.customerId)
                    .build()
                
                val publicProducts = SpecBuilder.of<Product>()
                    .isNull(Product::customerId)
                    .build()
                
                builder.custom(userProducts or publicProducts)
            } else {
                // 只查询用户自己的产品
                builder.withCurrentUserPermission(Product::customerId)
            }
        }
        
        return builder.build()
    }
}
```

## API 参考

### SpecBuilder 权限方法

#### 静态工厂方法
```kotlin
// 创建带权限控制的构建器
fun <T> withUserPermission(customerIdProperty: KProperty1<T, Long>): SpecBuilder<T>
fun <T> withUserPermissionNullable(customerIdProperty: KProperty1<T, Long?>): SpecBuilder<T>
```

#### 实例方法
```kotlin
// 添加当前用户权限控制
fun withCurrentUserPermission(customerIdProperty: KProperty1<T, Long>): SpecBuilder<T>
fun withCurrentUserPermissionNullable(customerIdProperty: KProperty1<T, Long?>): SpecBuilder<T>

// 添加租户权限控制
fun withBizPermission(bizIdProperty: KProperty1<T, Long>): SpecBuilder<T>
```

## 最佳实践

### 1. 默认启用权限控制
```kotlin
// 推荐：默认使用带权限的构建器
SpecBuilder.withUserPermission(Entity::customerId)

// 而不是：
SpecBuilder.of<Entity>().withCurrentUserPermission(Entity::customerId)
```

### 2. 权限控制前置
```kotlin
// 推荐：权限控制放在最前面
return SpecBuilder.withUserPermission(SubOrder::customerId)
    .like(SubOrder::orderNo, orderNo)
    .build()

// 而不是：
return SpecBuilder.of<SubOrder>()
    .like(SubOrder::orderNo, orderNo)
    .withCurrentUserPermission(SubOrder::customerId)  // 容易遗漏
    .build()
```

### 3. 明确权限边界
```kotlin
// 明确标注权限控制的意图
override fun convertToSpec(): Specification<SubOrder> {
    // 权限控制：用户只能查询自己的订单
    return SpecBuilder.withUserPermission(SubOrder::customerId)
        .like(SubOrder::orderNo, orderNo)
        .build()
}
```

### 4. 测试权限控制
```kotlin
@Test
fun `should only return user own orders`() {
    // 设置用户上下文
    val userContext = UserContext(id = 1L, customerId = 100L, ...)
    UserContextHolder.set(userContext)
    
    // 执行查询
    val request = SubOrderPageRequest(orderNo = "ORD")
    val spec = request.convertToSpec()
    
    // 验证权限控制是否生效
    // 应该包含 customerId = 100 的条件
}
```

## 安全注意事项

1. **权限控制不可绕过**：所有涉及用户数据的查询都必须添加权限控制
2. **测试覆盖**：确保权限控制逻辑有充分的测试覆盖
3. **日志记录**：在关键权限检查点添加日志记录
4. **异常处理**：当用户上下文为空时的处理策略

## 故障排查

### 常见问题

1. **查询结果为空**
   - 检查 UserContext 是否正确设置
   - 确认 customerId 字段值是否正确

2. **权限控制不生效**
   - 确认是否使用了正确的权限控制方法
   - 检查属性引用是否正确

3. **编译错误**
   - 确认属性类型匹配（Long vs Long?）
   - 检查 import 语句是否正确
