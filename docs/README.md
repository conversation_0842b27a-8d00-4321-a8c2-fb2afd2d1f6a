# Lux Customer Platform Backend

## 项目概览

Lux Customer Platform Backend 是一个基于 Spring Boot 和 Kotlin 的客户平台后端服务，提供客户账户管理、认证授权等核心功能。

## 技术栈

- **语言**: Kotlin 1.9.25
- **框架**: Spring Boot 3.5.3
- **数据库**: PostgreSQL + Redis
- **构建工具**: Gradle 8.14.3
- **JDK版本**: Java 21
- **ORM**: Spring Data JPA + Hibernate

## 核心依赖

```kotlin
// 认证相关
implementation("com.auth0:java-jwt:4.5.0")

// Spring Boot 核心
implementation("org.springframework.boot:spring-boot-starter-data-jpa")
implementation("org.springframework.boot:spring-boot-starter-data-redis")
implementation("org.springframework.boot:spring-boot-starter-web")

// Kotlin 支持
implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
implementation("org.jetbrains.kotlin:kotlin-reflect")

// 日志
implementation("io.github.oshai:kotlin-logging-jvm:latest.release")
```

## 项目结构

```
src/main/kotlin/io/cliveyou/luxcustomerplatformbackend/
├── LuxCustomerPlatformBackendApplication.kt    # 应用启动类
├── application/                                # 应用服务层
│   └── UserLoginApplicationService.kt
├── common/                                     # 公共组件
│   ├── enums/                                 # 枚举定义
│   ├── exception/                             # 异常处理
│   └── utils/                                 # 工具类
├── config/                                    # 配置类
│   ├── JacksonSelfConfiguration.kt           # JSON序列化配置
│   ├── auth/                                 # 认证配置
│   ├── jpa/                                  # JPA配置
│   └── web/                                  # Web配置
├── domain/                                    # 领域模型
│   └── CustomerPlatformAccount.kt
├── facade/                                    # 接口层
│   └── user/
│       └── UserController.kt
└── infrastructure/                            # 基础设施层
    └── repository/
        ├── jpa/                              # JPA仓储
        └── redis/                            # Redis仓储
```

## 快速开始

### 环境要求

- JDK 21+
- PostgreSQL 12+
- Redis 6+
- Gradle 8.14.3+

### 本地开发环境配置

1. **数据库配置**
   ```yaml
   # application-local.yaml
   spring:
     datasource:
       url: *************************************
       username: postgres
       password: root
   ```

2. **Redis配置**
   ```yaml
   spring:
     data:
       redis:
         url: redis://localhost:6379
         username: default
   ```

### 启动应用

```bash
# 克隆项目
git clone <repository-url>
cd lux-customer-platform-backend

# 构建项目
./gradlew build

# 启动应用
./gradlew bootRun
```

应用将在 `http://localhost:8080/api` 启动

## 核心功能

### 1. 用户认证
- 基于JWT的用户登录/登出
- Cookie-based认证机制
- Redis会话管理

### 2. 客户账户管理
- 客户平台账户CRUD操作
- 多租户支持(bizId)
- 账户状态管理

### 3. 安全特性
- API签名验证
- CORS跨域支持
- 全局异常处理

## API接口

### 认证接口
- `POST /api/users/login` - 用户登录
- `POST /api/users/logout` - 用户登出
- `GET /api/users/profile` - 获取用户信息

### 配置说明

- **应用名称**: toms
- **上下文路径**: /api
- **默认环境**: local
- **数据库连接池**: HikariCP
- **JPA配置**: DDL自动更新，SQL日志开启

## 开发规范

1. **分层架构**: 严格按照 facade -> application -> domain -> infrastructure 分层
2. **包命名**: 使用 `io.cliveyou.luxcustomerplatformbackend` 作为根包
3. **异常处理**: 统一使用 GlobalExceptionHandler 处理异常
4. **日志记录**: 使用 kotlin-logging 进行日志记录
5. **数据序列化**: Long类型统一序列化为字符串

## 相关文档

- [架构设计](./ARCHITECTURE.md)
- [API规范](./API_SPECIFICATION.md)
- [数据库设计](./DATABASE_SCHEMA.md)
- [开发指南](./DEVELOPMENT_GUIDE.md)