# 国际化 (i18n) 实现指南

## 概述

本项目使用 `react-i18next` 实现国际化功能，支持英文、中文、越南语三种语言。默认语言为英文，用户可以通过界面切换语言，选择会自动保存到 localStorage 中实现持久化。

## 技术栈

- **i18next**: 核心国际化框架
- **react-i18next**: React 集成
- **i18next-browser-languagedetector**: 浏览器语言检测

## 项目结构

```
src/
├── lib/
│   └── i18n.ts                    # i18n 配置文件
├── locales/                       # 翻译文件目录
│   ├── en.json                   # 英文翻译
│   ├── zh.json                   # 中文翻译
│   └── vi.json                   # 越南语翻译
├── components/
│   └── ui/
│       └── language-switcher.tsx  # 语言切换组件
└── main.tsx                      # 应用入口 (初始化 i18n)
```

## 配置说明

### i18n 配置 (`src/lib/i18n.ts`)

```typescript
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: enTranslation },
      zh: { translation: zhTranslation },
      vi: { translation: viTranslation },
    },
    fallbackLng: 'en',              // 默认语言
    detection: {
      order: ['localStorage', 'navigator'],
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage'],
    },
    supportedLngs: ['en', 'zh', 'vi'],
  });
```

### 语言检测优先级

1. **localStorage**: 用户之前选择的语言
2. **navigator**: 浏览器语言设置
3. **fallback**: 默认英文

## 翻译文件结构

翻译文件采用嵌套的 JSON 结构，按功能模块组织：

```json
{
  "common": {
    "save": "保存",
    "cancel": "取消",
    "loading": "加载中..."
  },
  "auth": {
    "loginTitle": "TOMS 系统登录",
    "loginButton": "登录"
  },
  "navigation": {
    "dashboard": "仪表盘",
    "secret": "密钥"
  },
  "dashboard": {
    "home": "主页",
    "homeDescription": "可视化数据分析平台数据"
  }
}
```

## 使用方法

### 1. 在组件中使用翻译

```typescript
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('dashboard.home')}</h1>
      <p>{t('dashboard.homeDescription')}</p>
    </div>
  );
}
```

### 2. 带参数的翻译

```typescript
// 翻译文件中
{
  "messages": {
    "getUserInfoFailed": "获取用户信息失败: {{message}}"
  }
}

// 组件中使用
const errorMessage = t('messages.getUserInfoFailed', { message: error.message });
```

### 3. 语言切换组件

```typescript
import LanguageSwitcher from '@/components/ui/language-switcher';

function Header() {
  return (
    <div>
      <LanguageSwitcher 
        variant="ghost" 
        size="sm" 
        showText={false} 
      />
    </div>
  );
}
```

## 语言切换组件

### 组件属性

```typescript
interface LanguageSwitcherProps {
  variant?: 'default' | 'ghost' | 'outline';  // 按钮样式
  size?: 'default' | 'sm' | 'lg' | 'icon';    // 按钮大小
  showText?: boolean;                          // 是否显示语言文本
  className?: string;                          // 自定义样式类
}
```

### 使用位置

1. **登录页面**: 右上角，带边框样式
2. **Dashboard 头部**: 通知铃铛旁边，仅显示图标

## 支持的语言

| 语言代码 | 语言名称 | 本地化名称 |
|---------|---------|-----------|
| `en`    | English | English   |
| `zh`    | Chinese | 中文      |
| `vi`    | Vietnamese | Tiếng Việt |

## 持久化存储

- **存储位置**: `localStorage`
- **存储键**: `i18nextLng`
- **存储值**: 语言代码 (`en`, `zh`, `vi`)

用户选择的语言会自动保存到 localStorage，下次访问时会自动恢复。

## 开发指南

### 添加新的翻译

1. 在所有语言文件中添加相同的键值对
2. 使用嵌套结构组织翻译
3. 保持键名的一致性

### 添加新语言

1. 创建新的翻译文件 `src/locales/{lang}.json`
2. 在 `src/lib/i18n.ts` 中导入并添加到 resources
3. 在 `supportedLngs` 数组中添加语言代码
4. 在 `LanguageSwitcher` 组件中添加语言选项

### 最佳实践

1. **键名规范**: 使用点分隔的层级结构，如 `auth.loginTitle`
2. **模块化**: 按功能模块组织翻译键
3. **一致性**: 确保所有语言文件包含相同的键
4. **参数化**: 对于动态内容使用插值语法 `{{variable}}`
5. **回退机制**: 始终提供英文翻译作为回退

## 故障排除

### 常见问题

1. **翻译不显示**: 检查键名是否正确，是否在所有语言文件中都存在
2. **语言切换无效**: 确认 localStorage 权限，检查浏览器控制台错误
3. **默认语言错误**: 检查 `fallbackLng` 配置和语言检测顺序

### 调试技巧

1. 在 i18n 配置中启用 `debug: true`
2. 检查浏览器 localStorage 中的 `i18nextLng` 值
3. 使用浏览器开发工具查看网络请求和控制台日志

## 更新日志

### v1.0.0 (2024-01-16)
- ✅ 实现基础国际化功能
- ✅ 支持英文、中文、越南语
- ✅ 添加语言切换组件
- ✅ 实现 localStorage 持久化
- ✅ 集成到登录页面和 Dashboard
- ✅ 更新现有组件使用 i18n

## 相关文件

- `src/lib/i18n.ts` - i18n 配置
- `src/locales/*.json` - 翻译文件
- `src/components/ui/language-switcher.tsx` - 语言切换组件
- `src/main.tsx` - 应用入口初始化
- `src/app/login/page.tsx` - 登录页面集成
- `src/components/layout/main-layout.tsx` - Dashboard 布局集成
