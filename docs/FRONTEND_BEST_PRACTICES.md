# 前端开发最佳实践

## 组件设计最佳实践

### 1. 组件职责单一原则

每个组件应该只负责一个功能，保持简单和可复用：

```tsx
// ❌ 不好的做法 - 组件职责过多
const UserDashboard = () => {
  // 用户信息获取
  // 数据表格渲染
  // 表单处理
  // 模态框管理
  // ...
}

// ✅ 好的做法 - 拆分为多个组件
const UserDashboard = () => {
  return (
    <div>
      <UserProfile />
      <UserDataTable />
      <UserActions />
    </div>
  )
}
```

### 2. Props 接口设计

设计清晰、可扩展的 Props 接口：

```tsx
// ✅ 好的 Props 设计
interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  loading?: boolean
  error?: string
  pagination?: {
    page: number
    pageSize: number
    total: number
  }
  onPageChange?: (page: number) => void
  onRowClick?: (row: T) => void
  className?: string
}

// ✅ 使用联合类型限制选项
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost'
  size: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  children: React.ReactNode
}
```

### 3. 自定义 Hooks 最佳实践

将复杂逻辑抽取到自定义 Hooks 中：

```tsx
// ✅ 数据获取 Hook
export const useUserData = (userId: string) => {
  const [data, setData] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true)
        const user = await userApi.getById(userId)
        setData(user)
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取用户失败')
      } finally {
        setLoading(false)
      }
    }

    fetchUser()
  }, [userId])

  return { data, loading, error, refetch: () => fetchUser() }
}

// ✅ 表单状态管理 Hook
export const useFormWithValidation = <T extends FieldValues>(
  schema: ZodSchema<T>,
  defaultValues?: Partial<T>
) => {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (
    onSubmit: (data: T) => Promise<void>
  ) => {
    return form.handleSubmit(async (data) => {
      try {
        setIsSubmitting(true)
        await onSubmit(data)
      } finally {
        setIsSubmitting(false)
      }
    })
  }

  return {
    ...form,
    isSubmitting,
    handleSubmit
  }
}
```

## 状态管理最佳实践

### 1. Jotai 原子化状态设计

```tsx
// ✅ 原子化状态设计
// src/state/user-state.ts
export const userAtom = atom<User | null>(null)
export const userLoadingAtom = atom(false)
export const userErrorAtom = atom<string | null>(null)

// 派生状态
export const isAuthenticatedAtom = atom((get) => get(userAtom) !== null)
export const userRoleAtom = atom((get) => get(userAtom)?.role || 'guest')

// 异步操作原子
export const loginAtom = atom(
  null,
  async (get, set, credentials: LoginCredentials) => {
    set(userLoadingAtom, true)
    set(userErrorAtom, null)
    
    try {
      const user = await userApi.login(credentials)
      set(userAtom, user)
      localStorage.setItem(userTokenKey, user.token)
    } catch (error) {
      set(userErrorAtom, error.message)
    } finally {
      set(userLoadingAtom, false)
    }
  }
)
```

### 2. 表格状态管理

```tsx
// ✅ 表格状态管理最佳实践
export const useTableState = <T>() => {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10
  })
  
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [globalFilter, setGlobalFilter] = useState('')

  // URL 同步
  const [searchParams, setSearchParams] = useSearchParams()
  
  useEffect(() => {
    const params = new URLSearchParams()
    params.set('page', String(pagination.pageIndex + 1))
    params.set('size', String(pagination.pageSize))
    if (globalFilter) params.set('search', globalFilter)
    setSearchParams(params)
  }, [pagination, globalFilter, setSearchParams])

  return {
    pagination,
    setPagination,
    sorting,
    setSorting,
    columnFilters,
    setColumnFilters,
    globalFilter,
    setGlobalFilter
  }
}
```

## API 层最佳实践

### 1. 统一的 API 响应处理

```tsx
// ✅ 统一的响应类型
export interface ApiResponse<T = any> {
  success: boolean
  code: string
  message: string
  data: T
  timestamp: number
}

export interface PaginatedResponse<T> {
  content: T[]
  total: number
  page: number
  size: number
  totalPages: number
}

// ✅ API 客户端封装
class ApiClient {
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await axiosInstance.request<ApiResponse<T>>(config)
      
      if (!response.data.success) {
        throw new Error(response.data.message)
      }
      
      return response.data.data
    } catch (error) {
      // 统一错误处理
      throw this.handleError(error)
    }
  }

  private handleError(error: any): Error {
    if (error.response?.status === 401) {
      // 处理认证失败
      this.handleAuthError()
    }
    
    return new Error(
      error.response?.data?.message || 
      error.message || 
      '请求失败'
    )
  }
}
```

### 2. API 函数设计模式

```tsx
// ✅ RESTful API 设计模式
export const createApiModule = <T, CreateT = Partial<T>, UpdateT = Partial<T>>(
  baseUrl: string
) => ({
  // 获取列表
  list: async (params?: {
    page?: number
    size?: number
    search?: string
    filters?: Record<string, any>
  }): Promise<PaginatedResponse<T>> => {
    return apiClient.get({
      url: baseUrl,
      params
    })
  },

  // 获取详情
  getById: async (id: string): Promise<T> => {
    return apiClient.get({
      url: `${baseUrl}/${id}`
    })
  },

  // 创建
  create: async (data: CreateT): Promise<T> => {
    return apiClient.post({
      url: baseUrl,
      data
    })
  },

  // 更新
  update: async (id: string, data: UpdateT): Promise<T> => {
    return apiClient.put({
      url: `${baseUrl}/${id}`,
      data
    })
  },

  // 删除
  delete: async (id: string): Promise<void> => {
    return apiClient.delete({
      url: `${baseUrl}/${id}`
    })
  }
})

// 使用示例
export const userApi = createApiModule<User, CreateUserRequest, UpdateUserRequest>('/api/users')
```

## 表单处理最佳实践

### 1. 复杂表单设计

```tsx
// ✅ 分步表单设计
export const MultiStepForm = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<Partial<CompleteFormData>>({})

  const steps = [
    { component: BasicInfoStep, schema: basicInfoSchema },
    { component: ContactStep, schema: contactSchema },
    { component: PreferencesStep, schema: preferencesSchema }
  ]

  const currentSchema = steps[currentStep].schema
  const CurrentStepComponent = steps[currentStep].component

  const form = useForm({
    resolver: zodResolver(currentSchema),
    defaultValues: formData
  })

  const handleNext = async (data: any) => {
    setFormData(prev => ({ ...prev, ...data }))
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1)
    } else {
      // 提交完整表单
      await submitCompleteForm({ ...formData, ...data })
    }
  }

  return (
    <div>
      <StepIndicator current={currentStep} total={steps.length} />
      <Form {...form}>
        <CurrentStepComponent onNext={handleNext} />
      </Form>
    </div>
  )
}
```

### 2. 表单验证最佳实践

```tsx
// ✅ 复杂验证规则
const userSchema = z.object({
  email: z
    .string()
    .email('请输入有效的邮箱地址')
    .refine(async (email) => {
      // 异步验证邮箱是否已存在
      const exists = await userApi.checkEmailExists(email)
      return !exists
    }, '该邮箱已被注册'),
    
  password: z
    .string()
    .min(8, '密码至少8位')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
    
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword']
})

// ✅ 条件验证
const organizationSchema = z.object({
  type: z.enum(['individual', 'company']),
  name: z.string().min(1, '请输入名称'),
  companyInfo: z.object({
    registrationNumber: z.string(),
    address: z.string()
  }).optional()
}).refine((data) => {
  if (data.type === 'company') {
    return data.companyInfo?.registrationNumber && data.companyInfo?.address
  }
  return true
}, {
  message: '企业用户必须填写注册号和地址',
  path: ['companyInfo']
})
```

## 性能优化最佳实践

### 1. 组件优化

```tsx
// ✅ 使用 memo 优化纯组件
export const UserCard = React.memo<UserCardProps>(({ user, onEdit }) => {
  return (
    <div className="p-4 border rounded">
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      <Button onClick={() => onEdit(user)}>编辑</Button>
    </div>
  )
}, (prevProps, nextProps) => {
  // 自定义比较函数
  return prevProps.user.id === nextProps.user.id &&
         prevProps.user.updatedAt === nextProps.user.updatedAt
})

// ✅ 使用 useMemo 优化计算
export const DataTable = ({ data, filters }) => {
  const filteredData = useMemo(() => {
    return data.filter(item => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true
        return item[key]?.toString().toLowerCase().includes(value.toLowerCase())
      })
    })
  }, [data, filters])

  const sortedData = useMemo(() => {
    return [...filteredData].sort((a, b) => {
      // 排序逻辑
    })
  }, [filteredData, sortConfig])

  return <Table data={sortedData} />
}
```

### 2. 懒加载和代码分割

```tsx
// ✅ 路由级别的懒加载
const Dashboard = lazy(() => import('@/app/dash/Dashboard'))
const UserManagement = lazy(() => import('@/app/dash/user/UserManagement'))
const KeyManagement = lazy(() => import('@/app/dash/key/KeyManagement'))

// ✅ 组件级别的懒加载
const HeavyChart = lazy(() => import('@/components/charts/HeavyChart'))

export const DashboardPage = () => {
  const [showChart, setShowChart] = useState(false)

  return (
    <div>
      <Button onClick={() => setShowChart(true)}>显示图表</Button>
      {showChart && (
        <Suspense fallback={<ChartSkeleton />}>
          <HeavyChart />
        </Suspense>
      )}
    </div>
  )
}
```

## 错误处理最佳实践

### 1. 全局错误边界

```tsx
// ✅ 全局错误边界
export const GlobalErrorBoundary = ({ children }: { children: React.ReactNode }) => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        // 错误上报
        console.error('Global error:', error, errorInfo)
        // 可以集成错误监控服务
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

const ErrorFallback = ({ error, resetErrorBoundary }: FallbackProps) => {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-red-600 mb-4">出现错误</h2>
        <p className="text-gray-600 mb-4">{error.message}</p>
        <Button onClick={resetErrorBoundary}>重试</Button>
      </div>
    </div>
  )
}
```

### 2. API 错误处理

```tsx
// ✅ 统一的错误处理 Hook
export const useApiError = () => {
  const { toast } = useToast()

  const handleError = useCallback((error: Error) => {
    // 根据错误类型显示不同的提示
    if (error.message.includes('网络')) {
      toast({
        title: '网络错误',
        description: '请检查网络连接后重试',
        variant: 'destructive'
      })
    } else if (error.message.includes('权限')) {
      toast({
        title: '权限不足',
        description: '您没有执行此操作的权限',
        variant: 'destructive'
      })
    } else {
      toast({
        title: '操作失败',
        description: error.message,
        variant: 'destructive'
      })
    }
  }, [toast])

  return { handleError }
}
```

## 可访问性最佳实践

### 1. 键盘导航

```tsx
// ✅ 支持键盘导航的组件
export const SearchableSelect = ({ options, onSelect }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [focusedIndex, setFocusedIndex] = useState(-1)

  const handleKeyDown = (e: KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setFocusedIndex(prev => 
          prev < options.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setFocusedIndex(prev => 
          prev > 0 ? prev - 1 : options.length - 1
        )
        break
      case 'Enter':
        if (focusedIndex >= 0) {
          onSelect(options[focusedIndex])
          setIsOpen(false)
        }
        break
      case 'Escape':
        setIsOpen(false)
        break
    }
  }

  return (
    <div onKeyDown={handleKeyDown}>
      {/* 组件实现 */}
    </div>
  )
}
```

### 2. ARIA 属性

```tsx
// ✅ 正确使用 ARIA 属性
export const DataTable = ({ data, loading }) => {
  return (
    <div role="region" aria-label="数据表格" aria-live="polite">
      {loading && (
        <div role="status" aria-label="正在加载数据">
          <Spinner />
        </div>
      )}
      <table role="table" aria-rowcount={data.length}>
        <thead>
          <tr role="row">
            <th role="columnheader" aria-sort="ascending">
              姓名
            </th>
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={item.id} role="row" aria-rowindex={index + 1}>
              <td role="gridcell">{item.name}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
```

## 国际化最佳实践

虽然当前项目主要使用中文，但建议为未来的国际化做准备：

```tsx
// ✅ 为国际化做准备的文本管理
export const messages = {
  common: {
    save: '保存',
    cancel: '取消',
    delete: '删除',
    edit: '编辑',
    loading: '加载中...'
  },
  user: {
    profile: '用户资料',
    settings: '设置',
    logout: '退出登录'
  }
} as const

// 使用类型安全的文本引用
type MessageKey = keyof typeof messages.common | `user.${keyof typeof messages.user}`

export const useMessage = () => {
  const getMessage = (key: MessageKey): string => {
    const keys = key.split('.')
    let value: any = messages
    
    for (const k of keys) {
      value = value[k]
    }
    
    return value || key
  }

  return { getMessage }
}
```

---

遵循这些最佳实践将帮助团队构建更加健壮、可维护和高性能的前端应用。
