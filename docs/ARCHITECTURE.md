# 架构设计文档

## 整体架构

Lux Customer Platform Backend 采用经典的分层架构设计，遵循领域驱动设计(DDD)原则。

## 分层架构

### 1. Facade Layer (接口层)
**职责**: 对外提供REST API接口，处理HTTP请求和响应

**主要组件**:
- `UserController`: 用户相关接口
- Request/Response DTO类
- 参数验证和格式转换

**设计原则**:
- 只负责协议转换，不包含业务逻辑
- 统一的异常处理和响应格式
- 支持CORS和认证拦截

### 2. Application Layer (应用服务层)
**职责**: 编排业务流程，协调领域对象完成业务用例

**主要组件**:
- `UserLoginApplicationService`: 用户登录业务流程
- 事务管理
- 业务流程编排

**设计原则**:
- 无状态服务
- 事务边界控制
- 业务流程编排，不包含核心业务逻辑

### 3. Domain Layer (领域层)
**职责**: 核心业务逻辑和业务规则

**主要组件**:
- `CustomerPlatformAccount`: 客户平台账户实体
- 业务枚举类型
- 领域服务

**设计原则**:
- 富领域模型
- 封装业务规则
- 独立于技术实现

### 4. Infrastructure Layer (基础设施层)
**职责**: 提供技术实现，支撑上层业务

**主要组件**:
- JPA Repository: 数据持久化
- Redis Repository: 缓存和会话管理
- 配置类和工具类

**设计原则**:
- 技术实现细节
- 可替换的技术组件
- 支撑业务层运行

## 核心设计模式

### 1. Repository Pattern
```kotlin
@Repository
interface CustomerPlatformAccountRepository : JpaRepository<CustomerPlatformAccount, Long> {
    fun findByEmailAndBizId(email: String, bizId: Long): CustomerPlatformAccount?
    fun findByEmailAndPasswordAndBizId(email: String, password: String, bizId: Long): CustomerPlatformAccount?
}
```

### 2. Service Pattern
```kotlin
@Service
class UserLoginApplicationService(
    private val userRepository: UserRepository,
    private val userRedisRepository: UserRedisRepository,
) {
    fun login(request: UserLoginRequest): String {
        // 业务流程编排
    }
}
```

### 3. Interceptor Pattern
```kotlin
@Component
class ApiSignatureInterceptor : HandlerInterceptor {
    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Boolean {
        // 认证逻辑
    }
}
```

## 技术架构

### 1. 认证授权架构
```
Client Request
    ↓
ApiSignatureInterceptor (Cookie-based JWT验证)
    ↓
UserContextHolder (线程本地用户上下文)
    ↓
Business Logic
```

**特点**:
- JWT Token存储在HttpOnly Cookie中
- Redis存储用户会话信息
- FastThreadLocal管理用户上下文

### 2. 数据访问架构
```
Application Service
    ↓
JPA Repository (主数据存储)
    ↓
PostgreSQL

Application Service
    ↓
Redis Repository (缓存/会话)
    ↓
Redis
```

### 3. 异常处理架构
```
Controller
    ↓
Business Logic (抛出CognitionWebException)
    ↓
@RestControllerAdvice GlobalExceptionHandler
    ↓
统一错误响应格式 (ErrorResult)
```

**异常处理层次**:
- **CognitionException**: 基础异常类
- **CognitionWebException**: Web业务异常，包含错误码和HTTP状态码
- **OmsBaseErrorCode**: 业务错误码枚举
- **ErrorResult**: 统一错误响应格式

## 配置管理

### 1. 多环境配置
- `application.yaml`: 基础配置
- `application-local.yaml`: 本地开发环境
- `application-prod.yaml`: 生产环境(待添加)

### 2. 核心配置类
- `WebConfig`: Web层配置(CORS、拦截器)
- `JacksonSelfConfiguration`: JSON序列化配置
- JPA配置: 实体扫描、事务管理

## 安全设计

### 1. 认证机制
- JWT Token认证
- Cookie-based存储(防XSS)
- Redis会话管理

### 2. 授权机制
- 基于用户上下文的权限控制
- 多租户隔离(bizId)
- API路径白名单

### 3. 数据安全
- 密码加密存储
- SQL注入防护(JPA)
- 敏感信息脱敏

## 业务异常处理机制

### 1. 异常体系设计

#### 基础异常接口
```kotlin
interface BaseErrorCodeEnum {
    val msg: String        // 错误消息
    val code: String       // 错误码
    val httpCode: HttpStatus  // HTTP状态码
}
```

#### 业务错误码枚举
```kotlin
enum class OmsBaseErrorCode(
    override val code: String,
    override val msg: String,
    override val httpCode: HttpStatus,
) : BaseErrorCodeEnum {
    INVALID_PASSWORD("100001", "Invalid password", HttpStatus.BAD_REQUEST),
    USER_NOT_FOUND("100002", "User not found", HttpStatus.NOT_FOUND),
    ACCOUNT_DISABLED("100003", "Account is disabled", HttpStatus.FORBIDDEN),
    TOKEN_EXPIRED("100004", "Token has expired", HttpStatus.UNAUTHORIZED),
    INSUFFICIENT_PERMISSIONS("100005", "Insufficient permissions", HttpStatus.FORBIDDEN),
    EMAIL_ALREADY_EXISTS("100006", "Email already exists", HttpStatus.BAD_REQUEST),
    INVALID_EMAIL_FORMAT("100007", "Invalid email format", HttpStatus.BAD_REQUEST),
    ;
}
```

#### 异常类层次
```kotlin
// 基础异常类
open class CognitionException(override val message: String) : RuntimeException()

// Web业务异常类
open class CognitionWebException(
    private val baseErrorCodeEnum: BaseErrorCodeEnum,
    vararg args: Any?,
) : CognitionException(baseErrorCodeEnum.msg.format(*args.map { it?.toString() ?: "" }.toTypedArray())) {
    fun code(): String = baseErrorCodeEnum.code
    fun httpCode(): HttpStatus = baseErrorCodeEnum.httpCode
    fun msg(): String = message
}
```

### 2. 异常抛出方式

#### 在Application Service中抛出业务异常
```kotlin
@Service
class UserLoginApplicationService(
    private val userRepository: UserRepository,
    private val userRedisRepository: UserRedisRepository,
) {
    fun login(request: UserLoginRequest): String {
        // 验证邮箱格式
        if (!isValidEmail(request.account)) {
            throw CognitionWebException(OmsBaseErrorCode.INVALID_EMAIL_FORMAT)
        }

        // 查找用户
        val user = userRepository.findByEmailAndPasswordAndBizId(
            request.account,
            request.password,
            getCurrentBizId()
        ) ?: throw CognitionWebException(OmsBaseErrorCode.INVALID_PASSWORD)

        // 检查账户状态
        if (user.status != BaseStatus.ENABLED) {
            throw CognitionWebException(OmsBaseErrorCode.ACCOUNT_DISABLED)
        }

        // 生成Token并保存会话
        val token = Jwt.createToken(user.id)
        userRedisRepository.saveUser(user, token)

        return token
    }
}
```

#### 在Controller中处理参数验证异常
```kotlin
@RestController
@RequestMapping("/users")
class UserController(
    private val userLoginApplicationService: UserLoginApplicationService,
) {
    @PostMapping("/login")
    fun login(@RequestBody request: UserLoginRequest): String {
        // 参数验证
        if (request.account.isBlank()) {
            throw CognitionWebException(OmsBaseErrorCode.INVALID_EMAIL_FORMAT)
        }

        return userLoginApplicationService.login(request)
    }
}
```

#### 带参数的异常消息
```kotlin
// 错误码定义支持参数化消息
enum class OmsBaseErrorCode(
    override val code: String,
    override val msg: String,
    override val httpCode: HttpStatus,
) : BaseErrorCodeEnum {
    USER_OPERATION_FAILED("100008", "User %s operation failed: %s", HttpStatus.BAD_REQUEST),
    ;
}

// 抛出异常时传入参数
throw CognitionWebException(OmsBaseErrorCode.USER_OPERATION_FAILED, userId, "login")
// 最终消息: "User 123456 operation failed: login"
```

### 3. 全局异常处理器

```kotlin
@RestControllerAdvice
class BiminiGlobalExceptionHandler {

    // 处理业务异常
    @ExceptionHandler(CognitionWebException::class)
    fun handleCognitionWebException(ex: CognitionWebException): ResponseEntity<ErrorResult> {
        log.error(ex) { "业务异常: ${ex.code()} - ${ex.msg()}" }
        return ResponseEntity
            .status(ex.httpCode())
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(ex))
    }

    // 处理通用认知异常
    @ExceptionHandler(CognitionException::class)
    fun handleCognitionException(ex: CognitionException): ResponseEntity<ErrorResult> {
        log.error(ex) { "认知系统异常: ${ex.message}" }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, ex.message))
    }

    // 处理参数验证异常
    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(ex: IllegalArgumentException): ResponseEntity<ErrorResult> {
        log.error(ex) { "参数验证异常: ${ex.message}" }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(MediaType.APPLICATION_JSON)
            .body(ErrorResult.of(HttpStatus.BAD_REQUEST, ex.message ?: "Illegal Argument"))
    }
}
```

### 4. 错误响应格式

#### ErrorResult统一响应格式
```kotlin
data class ErrorResult(
    val code: String,      // 业务错误码
    val msg: String,       // 错误消息
    val data: Any? = null, // 附加数据(可选)
) {
    companion object {
        // 从CognitionWebException创建
        fun of(error: CognitionWebException, data: Any? = null) =
            ErrorResult(error.code(), error.msg(), data)

        // 从HttpStatus创建
        fun of(httpStatus: HttpStatus, msg: String, data: Any? = null) =
            ErrorResult(httpStatus.value().toString(), msg, data)
    }
}
```

#### 客户端收到的错误响应示例
```json
{
  "code": "100001",
  "msg": "Invalid password",
  "data": null
}
```

### 5. 错误码管理规范

#### 错误码分类
```kotlin
object ErrorPrefix {
    const val PREFIX = "00"    // 通用错误
    const val AUTH = "10"      // 认证相关错误
    const val USER = "11"      // 用户相关错误
    const val ACCOUNT = "12"   // 账户相关错误
    const val PERMISSION = "13" // 权限相关错误
}

// 使用前缀组织错误码
enum class AuthErrorCode(
    override val code: String,
    override val msg: String,
    override val httpCode: HttpStatus,
) : BaseErrorCodeEnum {
    INVALID_TOKEN("10001", "Invalid token", HttpStatus.UNAUTHORIZED),
    TOKEN_EXPIRED("10002", "Token expired", HttpStatus.UNAUTHORIZED),
    INSUFFICIENT_PERMISSIONS("10003", "Insufficient permissions", HttpStatus.FORBIDDEN),
}
```

### 6. 异常处理最佳实践

#### 异常抛出原则
1. **业务异常**: 使用CognitionWebException，包含明确的错误码
2. **参数异常**: 使用IllegalArgumentException，在Controller层验证
3. **系统异常**: 使用CognitionException，记录详细日志
4. **第三方异常**: 捕获并转换为业务异常

#### 异常处理层次
- **Controller层**: 参数验证异常，格式转换异常
- **Application层**: 业务逻辑异常，权限验证异常
- **Domain层**: 业务规则异常，状态验证异常
- **Infrastructure层**: 数据访问异常，外部服务异常

#### 日志记录规范
```kotlin
// 业务异常 - ERROR级别
log.error(ex) { "业务异常: ${ex.code()} - ${ex.msg()}" }

// 系统异常 - ERROR级别，包含堆栈
log.error(ex) { "系统异常: ${ex.message}" }

// 参数异常 - WARN级别
log.warn(ex) { "参数异常: ${ex.message}" }
```
