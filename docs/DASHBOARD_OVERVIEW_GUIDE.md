# Dashboard Overview 功能说明

## 功能概述

Dashboard Overview页面为客户提供了一个全面的订单数据概览和分析界面，包含以下主要功能：

### 1. 关键指标统计
- **总订单数**: 显示指定时间范围内的订单总数
- **已完成订单**: 显示已完成的订单数量和完成率
- **处理中订单**: 显示正在处理的订单数量
- **最近7天订单**: 显示最近一周的订单活跃度

### 2. 订单趋势分析
- **柱状图模式**: 显示每日订单数量的变化趋势
- **折线图模式**: 更清晰地展示订单数量的变化趋势
- **详细信息**: 包含每日的完成、处理中、取消订单数量

### 3. 状态分布图表
- **饼图展示**: 直观显示不同状态订单的分布比例
- **详细统计**: 显示每种状态的具体数量和占比

### 4. 时间范围选择
- **快速选择**: 最近7天、30天、90天
- **自定义范围**: 支持选择任意时间范围
- **实时更新**: 选择时间范围后自动刷新数据

## 技术实现

### 后端架构

#### 1. 控制器层 (Controller)
```kotlin
@RestController
@RequestMapping("/dashboard")
class DashboardController
```
- `/dashboard/overview` - 获取概览统计
- `/dashboard/order-trend` - 获取订单趋势数据

#### 2. 应用服务层 (Application Service)
```kotlin
@Service
class DashboardApplicationService
```
- `getOverviewStats()` - 计算概览统计数据
- `getOrderTrend()` - 生成趋势分析数据

#### 3. 数据传输对象 (DTO)
- `DashboardStatsRequest` - 统计查询请求
- `DashboardOverviewResponse` - 概览统计响应
- `DashboardOrderTrendResponse` - 趋势数据响应

### 前端架构

#### 1. API服务层
```typescript
// src/api/dash/dash-api.tsx
export const dashboardApi = {
  getOverview: (request) => Promise<DashboardOverviewResponse>
  getOrderTrend: (request) => Promise<DashboardOrderTrendResponse>
}
```

#### 2. 组件层
- `OverviewStats` - 统计卡片组件
- `OrderTrendChart` - 订单趋势图表组件
- `StatusDistributionChart` - 状态分布图表组件
- `DateRangePicker` - 日期范围选择器组件

#### 3. 页面层
```typescript
// src/app/dash/home/<USER>
export default function HomePage()
```

## 权限控制

### 行级安全
- 使用 `@RequireUserPermission` 注解自动注入用户权限控制
- 用户只能查看自己的订单数据
- 所有统计和趋势分析都基于用户有权限的数据

### 实现机制
```kotlin
@RequireUserPermission
data class DashboardStatsRequest(
    val startDate: Long? = null,
    val endDate: Long? = null,
) : SpecRequest<SubOrder>
```

## 数据计算逻辑

### 1. 概览统计
- **总订单数**: 查询时间范围内的订单总数
- **完成率**: (已完成订单数 / 总订单数) × 100%
- **状态分布**: 按订单状态分组统计
- **最近7天**: 统计最近7天内的订单数量

### 2. 趋势分析
- **按日分组**: 将订单按创建日期分组
- **数据填充**: 为没有订单的日期填充0值
- **增长率计算**: 比较前后两个时间段的订单数量变化

### 3. 性能优化
- **数据缓存**: 前端缓存查询结果，避免重复请求
- **懒加载**: 图表组件支持加载状态显示
- **错误处理**: 完善的错误提示和重试机制

## 使用说明

### 1. 访问页面
- 登录系统后，点击侧边栏的"首页"或直接访问 `/dashboard/home`

### 2. 选择时间范围
- 使用右上角的时间范围选择器
- 可选择预设的时间范围或自定义范围
- 选择后数据会自动刷新

### 3. 查看统计信息
- **概览统计**: 查看关键指标卡片
- **趋势分析**: 切换到"趋势分析"标签页查看详细图表
- **状态分布**: 在概览页面查看订单状态分布饼图

### 4. 交互功能
- **图表悬停**: 鼠标悬停在图表上查看详细数据
- **图表类型**: 在趋势分析页面可以查看折线图模式
- **响应式设计**: 支持不同屏幕尺寸的设备

## 扩展功能

### 未来可能的增强
1. **导出功能**: 支持导出统计报表
2. **对比分析**: 支持不同时间段的对比
3. **预警功能**: 订单异常情况的预警提示
4. **更多维度**: 按地区、产品类型等维度的统计分析
5. **实时更新**: WebSocket实时推送订单状态变化

### 自定义配置
- 图表颜色主题可通过CSS变量调整
- 统计指标可根据业务需求扩展
- 时间范围选项可配置化

## 故障排除

### 常见问题
1. **数据不显示**: 检查时间范围选择是否正确
2. **图表加载失败**: 检查网络连接和API响应
3. **权限错误**: 确认用户登录状态和权限设置

### 调试方法
- 查看浏览器控制台的错误信息
- 检查网络请求的响应状态
- 验证后端日志中的权限控制逻辑
