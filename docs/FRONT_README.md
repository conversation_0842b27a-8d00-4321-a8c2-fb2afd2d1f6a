# 前端项目文档

本目录包含前端项目的完整开发文档和规范指南。

## 📚 文档目录

### 核心开发规范
- **[前端开发规范](./FRONTEND_DEVELOPMENT_GUIDE.md)** - 完整的前端开发规范文档
- **[前端开发最佳实践](./FRONTEND_BEST_PRACTICES.md)** - 开发最佳实践和模式
- **[组件开发指南](./COMPONENT_DEVELOPMENT_GUIDE.md)** - 组件设计和开发规范
- **[项目结构说明](./PROJECT_STRUCTURE.md)** - 详细的项目架构和目录结构

## 🎯 文档用途

这些文档旨在为前端开发团队提供：

1. **统一的开发标准** - 确保代码风格和架构的一致性
2. **最佳实践指导** - 提供经过验证的开发模式和技巧
3. **快速上手指南** - 帮助新团队成员快速了解项目结构
4. **维护参考** - 为项目维护和扩展提供指导

## 🛠️ 技术栈

- **框架**: React 19.0.0 + TypeScript 5.8
- **构建工具**: Vite 6.3.5
- **包管理器**: Bun
- **样式**: Tailwind CSS v4
- **UI 组件**: Radix UI + shadcn/ui
- **状态管理**: Jotai
- **路由**: React Router v7
- **表单**: React Hook Form + Zod
- **HTTP 客户端**: Axios

## 📖 如何使用这些文档

1. **新项目开始**: 先阅读 [前端开发规范](./FRONTEND_DEVELOPMENT_GUIDE.md)
2. **了解架构**: 查看 [项目结构说明](./PROJECT_STRUCTURE.md)
3. **组件开发**: 参考 [组件开发指南](./COMPONENT_DEVELOPMENT_GUIDE.md)
4. **优化代码**: 应用 [前端开发最佳实践](./FRONTEND_BEST_PRACTICES.md)

## 🔄 文档维护

这些文档应该随着项目的发展而持续更新：

- 新的技术栈变更需要更新相关文档
- 新的最佳实践应该及时补充
- 团队反馈的问题需要在文档中体现
- 定期审查文档的准确性和完整性

## 📞 联系方式

如果您对文档有任何疑问或建议，请：

1. 在团队会议中提出
2. 通过项目管理工具反馈
3. 直接修改文档并提交更新

---

**最后更新**: 2025-01-16
**维护者**: 前端开发团队
