# SpecBuilder 使用指南

## 概述

`SpecBuilder` 是一个类型安全的 JPA Specification 构建器，用于替代之前的全局函数方式，提供更好的代码组织和类型安全性。

## 优化前后对比

### 优化前（全局函数方式）
```kotlin
// 问题：全局污染，命名冲突风险
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.equal
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.between
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.and

override fun convertToSpec(): Specification<SubOrder> {
    return equal(SubOrder::orderNo, orderNo) and
            equal(SubOrder::customerOrderNo, customerOrderNo) and
            between(SubOrder::createdAt, start, end)
}
```

### 优化后（SpecBuilder 方式）
```kotlin
// 优势：命名空间隔离，链式调用，更清晰
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.SpecBuilder

override fun convertToSpec(): Specification<SubOrder> {
    return SpecBuilder.of<SubOrder>()
        .like(SubOrder::orderNo, orderNo)
        .like(SubOrder::customerOrderNo, customerOrderNo)
        .between(SubOrder::createdAt, start, end)
        .build()
}
```

## 基本用法

### 1. 创建构建器
```kotlin
val builder = SpecBuilder.of<User>()
```

### 2. 链式调用添加条件
```kotlin
val spec = SpecBuilder.of<User>()
    .equal(User::name, "张三")
    .greaterThan(User::age, 18)
    .like(User::email, "gmail")
    .build()
```

### 3. 处理可空值
```kotlin
// 自动忽略 null 值，不会添加到查询条件中
val spec = SpecBuilder.of<User>()
    .equal(User::name, null)        // 被忽略
    .like(User::email, "")          // 被忽略
    .greaterThan(User::age, 18)     // 生效
    .build()
```

## 支持的查询类型

### 等值查询
```kotlin
.equal(User::name, "张三")
.equal(User::status, UserStatus.ACTIVE)
```

### 比较查询
```kotlin
.greaterThan(User::age, 18)
.lessThan(User::score, 100)
```

### 模糊查询
```kotlin
.like(User::email, "gmail")        // %gmail% (支持 String 和 String? 类型)
.leftLike(User::name, "张")         // %张
.rightLike(User::phone, "138")      // 138%
```

### 范围查询
```kotlin
.between(User::createdAt, startTime, endTime)
```

### 列表查询
```kotlin
.inList(User::status, listOf(UserStatus.ACTIVE, UserStatus.PENDING))
```

### 空值查询
```kotlin
.isNull(User::deletedAt)
.isNotNull(User::email)
```

### 自定义条件
```kotlin
.custom(Specification { root, query, cb ->
    cb.equal(root.get<String>("customField"), "customValue")
})
```

## 高级用法

### 1. OR 连接
```kotlin
val spec = SpecBuilder.of<User>()
    .like(User::name, keyword)
    .like(User::email, keyword)
    .buildOr()  // 使用 OR 连接
```

### 2. 复杂组合
```kotlin
val nameOrEmailSpec = SpecBuilder.of<User>()
    .like(User::name, keyword)
    .like(User::email, keyword)
    .buildOr()

val ageSpec = SpecBuilder.of<User>()
    .greaterThan(User::age, 18)
    .build()

val finalSpec = nameOrEmailSpec and ageSpec
```

### 3. 条件分支
```kotlin
val builder = SpecBuilder.of<User>()
    .equal(User::status, UserStatus.ACTIVE)

if (hasAdminRole) {
    builder.isNotNull(User::adminLevel)
}

if (searchKeyword.isNotBlank()) {
    builder.like(User::name, searchKeyword)
}

val spec = builder.build()
```

## 在 Request 类中的使用

```kotlin
data class UserPageRequest(
    val name: String? = null,
    val email: String? = null,
    val minAge: Int? = null,
    val maxAge: Int? = null,
    val status: UserStatus? = null,
    val startTime: Long? = null,
    val endTime: Long? = null
) : SpecRequest<User>, OmsPageRequest() {
    
    override fun convertToSpec(): Specification<User> {
        return SpecBuilder.of<User>()
            .like(User::name, name)
            .like(User::email, email)
            .greaterThan(User::age, minAge)
            .lessThan(User::age, maxAge)
            .equal(User::status, status)
            .between(User::createdAt, startTime, endTime)
            .build()
    }
}
```

## 优势总结

1. **命名空间隔离**：避免全局函数污染
2. **类型安全**：编译时检查属性类型
3. **链式调用**：代码更简洁易读
4. **自动空值处理**：null 值自动忽略
5. **灵活组合**：支持 AND/OR 连接
6. **扩展性好**：易于添加新的查询类型
7. **兼容性好**：避免使用已废弃的 API，面向未来

## 迁移指南

如果项目中已经使用了旧的全局函数，可以按以下步骤迁移：

1. 替换 import 语句
2. 使用 SpecBuilder.of<T>() 创建构建器
3. 将函数调用改为方法调用
4. 在最后调用 .build() 或 .buildOr()

旧代码：
```kotlin
equal(User::name, name) and like(User::email, email)
```

新代码：
```kotlin
SpecBuilder.of<User>()
    .equal(User::name, name)
    .like(User::email, email)
    .build()
```

## 权限控制集成

SpecBuilder 提供了完整的权限控制支持，确保用户只能查询自己有权限的数据。

### 快速开始
```kotlin
// 1. 在请求类上添加权限注解
@RequireUserPermission
data class SubOrderPageRequest(...) : SpecRequest<SubOrder>, OmsPageRequest() {
    override fun convertToSpec(): Specification<SubOrder> {
        return SpecBuilder.of<SubOrder>()
            .like(SubOrder::orderNo, orderNo)
            .build()
    }
}

// 2. 在 Controller 中使用权限控制
@PostMapping("/search")
fun search(@RequestBody request: SubOrderPageRequest): Page<SubOrder> {
    val spec = request.convertToSpecWithPermissions()  // 自动添加权限控制
    return repository.findAll(spec, request.toPageable())
}
```

### 权限控制方式对比

| 方式 | 优点 | 缺点 | 推荐度 |
|------|------|------|--------|
| 注解方式 | 声明式、简洁、不易遗漏 | 需要额外的处理器 | ⭐⭐⭐⭐⭐ |
| 工厂方法 | 编译时检查、类型安全 | 需要手动调用 | ⭐⭐⭐⭐ |
| 实例方法 | 灵活、可条件控制 | 容易遗漏 | ⭐⭐⭐ |

详细的权限控制使用指南请参考：[权限控制指南](./PERMISSION_CONTROL_GUIDE.md)

## 技术说明

### 兼容性改进

本实现避免了使用 Spring Data JPA 中已废弃的 `Specification.where(null)` 方法，该方法在 3.5 版本中被标记为 `@Deprecated`，并将在 4.0 版本中移除。

**替代方案**：
```kotlin
// 废弃的方式（将在 Spring Data JPA 4.0 中移除）
fun <T> empty(): Specification<T> = Specification.where(null)

// 推荐的方式（兼容未来版本）
fun <T> empty(): Specification<T> = Specification { _, _, cb -> cb.conjunction() }
```

`cb.conjunction()` 创建一个总是返回 true 的条件，等效于匹配所有记录，这是创建空 Specification 的标准方式。

### 类型支持

SpecBuilder 提供了完善的类型支持：

**字符串类型**：
- 支持 `String` 和 `String?` 类型的属性
- 模糊查询方法自动处理空值检查

**数值类型**：
- 支持所有实现 `Comparable` 接口的类型
- 包括 `Int`、`Long`、`BigDecimal`、`LocalDateTime` 等

**枚举类型**：
- 完全支持枚举类型的等值查询
- 支持枚举列表的 IN 查询

**示例**：
```kotlin
// 字符串属性（可空）
.like(SubOrder::orderNo, "ORD")           // orderNo: String?
.like(SubOrder::customerName, "张三")      // customerName: String

// 数值属性
.greaterThan(SubOrder::createdAt, startTime)  // createdAt: Long
.between(Product::price, minPrice, maxPrice)  // price: BigDecimal

// 枚举属性
.equal(SubOrder::status, SubOrderStatus.CREATED)  // status: SubOrderStatus
.inList(User::status, listOf(UserStatus.ACTIVE, UserStatus.PENDING))
```
