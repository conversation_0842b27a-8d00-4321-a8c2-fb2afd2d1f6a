# API 规范文档

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **认证方式**: Cookie-based JWT Token
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
成功的接口直接返回数据，不包装额外的code和message字段：
```json
{
  "id": "*********",
  "name": "用户名",
  "account": "<EMAIL>"
}
```

### 错误响应
```json
{
  "code": "400",
  "msg": "错误描述",
  "data": null
}
```

## 认证相关API

### 1. 用户登录
**接口**: `POST /users/login`

**请求参数**:
```json
{
  "account": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
"登录成功"
```

**说明**:
- 登录成功后会在Cookie中设置`authToken`
- Token有效期为7天
- Cookie设置为HttpOnly防止XSS攻击

### 2. 用户登出
**接口**: `POST /users/logout`

**请求参数**: 无

**响应**:
```json
"退出成功"
```

### 3. 获取用户信息
**接口**: `GET /users/profile`

**请求参数**: 无

**响应**:
```json
{
  "id": "*********",
  "token": "jwt_token_string",
  "name": "用户名",
  "account": "<EMAIL>",
  "bizId": 1,
  "customerId": 1001
}
```

## 子订单相关API

### 1. 分页查询子订单
**接口**: `POST /api/sub-orders/search`

**请求参数**:
```json
{
  "page": 0,
  "size": 10,
  "sortBy": "createdAt",
  "sortDirection": "desc",
  "orderNo": "ORD20240101001",
  "customerOrderNo": "CUST001",
  "start": *************,
  "end": *************
}
```

**响应**: 直接返回分页数据
```json
{
  "content": [
    {
      "id": "*********",
      "parentId": 0,
      "orderNo": "ORD20240101001",
      "customerOrderNo": "CUST001",
      "customerId": 1001,
      "customerName": "客户名称",
      "bizId": 1,
      "status": "CREATED",
      "waybillStatus": null,
      "source": "LUXOMS",
      "remark": "",
      "failedReason": null,
      "recipient": {
        "userName": "收件人姓名",
        "receiverName": "收件人姓名",
        "country": "美国",
        "state": "加利福尼亚州",
        "city": "洛杉矶",
        "address1": "123 Main St",
        "address2": "Apt 4B",
        "postcode": "90210",
        "phone": "+*********0",
        "email": "<EMAIL>"
      },
      "product": {
        "spu": "PROD001",
        "size": "M",
        "color": "红色",
        "qty": 2,
        "spuId": 1001,
        "skuId": 2001,
        "weight": 0.5,
        "price": 29.99,
        "currency": "USD",
        "name": "产品名称",
        "cnName": "产品中文名称",
        "title": "产品标题",
        "supplierId": 3001,
        "supplierName": "供应商名称",
        "detail": "产品详情",
        "customName": "自定义名称",
        "material": "棉质",
        "hsCode": "6109100000"
      },
      "shipping": {
        "channel": "SF",
        "shipMethod": "顺丰国际",
        "wayBillRelation": "SF*********",
        "deliveryMethod": "STANDARD",
        "waybillLabelUrl": "https://example.com/label.pdf"
      },
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "totalElements": 100,
  "totalPages": 10,
  "size": 10,
  "number": 0,
  "first": true,
  "last": false,
  "numberOfElements": 10,
  "empty": false
}
```

**说明**:
- 支持按订单号、客户订单号、时间范围等条件查询
- 自动应用用户权限控制，只能查询当前用户的订单
- 支持分页和排序

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证机制

### Cookie认证
- Cookie名称: `authToken`
- 有效期: 7天
- 属性: HttpOnly, Path=/
- 安全性: 生产环境需设置Secure=true

### JWT Token格式
```json
{
  "iss": "cognition-bank",
  "sub": "*********",
  "userId": *********,
  "iat": **********
}
```

## 错误处理

### 常见错误码
- `INVALID_CREDENTIALS`: 账号密码不正确
- `USER_NOT_FOUND`: 用户不存在
- `ACCOUNT_DISABLED`: 账户已禁用
- `TOKEN_EXPIRED`: Token已过期
- `INSUFFICIENT_PERMISSIONS`: 权限不足

### 异常响应示例
```json
{
  "code": "100001",
  "msg": "账号密码不正确",
  "data": null
}
```

## 数据格式说明

### 时间格式
- 创建时间/更新时间: ISO 8601格式 (`2024-01-01T00:00:00Z`)
- 登录时间: Unix时间戳 (毫秒)

### ID格式
- 所有ID字段均为Long类型，序列化为字符串
- 使用雪花算法生成唯一ID

### 枚举值
- `BaseStatus`: ENABLED(启用), DISABLED(禁用), DELETED(已删除)
- `SubOrderStatus`: CREATED(已创建), CANCELLED(已取消), COMPLETED(已完成), SPLIT(已拆分), SUPPLIER_MATCHED(供应商已匹配), FAILED(失败), TRACKING_*(物流跟踪状态)
- `WaybillChannel`: SF(顺丰), YTO(圆通), YT(云途), BZ(斑马), CAINIAO(菜鸟)
- `OrderSource`: LUXOMS(系统订单), IMPORT(导入订单)
- `DeliveryMethod`: STANDARD(标准配送), EXPRESS(快递配送)

## 分页参数

### 请求参数
- `page`: 页码，从0开始，默认0
- `size`: 每页大小，默认10，最大100
- `sortBy`: 排序字段名称
- `sortDirection`: 排序方向，`asc`或`desc`，默认`asc`

### 响应格式
分页查询直接返回Spring Data的Page对象：
```json
{
  "content": [
    {
      "id": "*********",
      "orderNo": "ORD20240101001",
      "customerOrderNo": "CUST001",
      "customerId": "1001",
      "customerName": "客户名称",
      "status": "CREATED",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "totalElements": 100,
  "totalPages": 5,
  "size": 10,
  "number": 0,
  "first": true,
  "last": false,
  "numberOfElements": 10,
  "empty": false
}
```

## 多租户支持

所有API都支持多租户隔离，通过`bizId`字段区分不同租户的数据。当前用户的`bizId`会自动从用户上下文中获取。

## 权限控制

### 行级权限控制
系统实现了自动的行级权限控制：

1. **用户权限控制**: 用户只能访问属于自己的数据（通过`customerId`字段控制）
2. **租户权限控制**: 用户只能访问自己租户的数据（通过`bizId`字段控制）

### 自动权限注入
- 查询接口会自动注入当前用户的`customerId`和`bizId`条件
- 无需在请求中手动指定这些权限字段
- 权限控制在数据库查询层面实现，确保数据安全

### 权限控制示例
```json
// 用户请求
{
  "orderNo": "ORD001",
  "page": 0,
  "size": 10
}

// 实际执行的查询条件（自动添加权限控制）
{
  "orderNo": "ORD001",
  "customerId": 1001,  // 自动添加
  "bizId": 1,          // 自动添加
  "page": 0,
  "size": 10
}
```

## 安全规范

### 1. 认证拦截
- 除登录/登出接口外，所有接口都需要认证
- 白名单路径: `/google/callback`, `/test/**`, `/api/openapi/**`, `/error`

### 2. CORS配置
- 允许所有来源 (`allowedOriginPatterns: *`)
- 允许所有HTTP方法
- 允许携带Cookie (`allowCredentials: true`)

### 3. 数据安全
- 密码字段不在响应中返回
- Long类型ID序列化为字符串避免精度丢失
- 空值字段在JSON中自动过滤
- 自动行级权限控制，用户只能访问自己的数据
- 多租户数据隔离，确保租户间数据安全
