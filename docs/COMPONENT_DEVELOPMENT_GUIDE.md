# 组件开发指南

## 组件分类和职责

### 1. UI 基础组件 (`src/components/ui/`)

基于 shadcn/ui 和 Radix UI 的基础组件，提供一致的设计系统。

```tsx
// ✅ 基础按钮组件示例
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary'
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
        icon: 'h-10 w-10'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, loading, children, disabled, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && <Spinner className="mr-2 h-4 w-4" />}
        {children}
      </Comp>
    )
  }
)
Button.displayName = 'Button'
```

### 2. 业务组件 (`src/components/`)

封装特定业务逻辑的可复用组件。

```tsx
// ✅ 用户选择器组件
interface UserSelectorProps {
  value?: string[]
  onChange: (userIds: string[]) => void
  multiple?: boolean
  placeholder?: string
  disabled?: boolean
  className?: string
}

export const UserSelector: React.FC<UserSelectorProps> = ({
  value = [],
  onChange,
  multiple = false,
  placeholder = '选择用户',
  disabled = false,
  className
}) => {
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState('')
  
  const { data: users, loading } = useUserList({ search })
  
  const selectedUsers = users?.filter(user => value.includes(user.id)) || []
  
  const handleSelect = (userId: string) => {
    if (multiple) {
      const newValue = value.includes(userId)
        ? value.filter(id => id !== userId)
        : [...value, userId]
      onChange(newValue)
    } else {
      onChange([userId])
      setOpen(false)
    }
  }
  
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('justify-between', className)}
          disabled={disabled}
        >
          {selectedUsers.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {selectedUsers.map(user => (
                <Badge key={user.id} variant="secondary">
                  {user.name}
                </Badge>
              ))}
            </div>
          ) : (
            placeholder
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <CommandInput
            placeholder="搜索用户..."
            value={search}
            onValueChange={setSearch}
          />
          <CommandEmpty>
            {loading ? '搜索中...' : '未找到用户'}
          </CommandEmpty>
          <CommandGroup className="max-h-64 overflow-auto">
            {users?.map(user => (
              <CommandItem
                key={user.id}
                value={user.id}
                onSelect={() => handleSelect(user.id)}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    value.includes(user.id) ? 'opacity-100' : 'opacity-0'
                  )}
                />
                <Avatar className="mr-2 h-6 w-6">
                  <AvatarImage src={user.avatar} />
                  <AvatarFallback>{user.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{user.name}</div>
                  <div className="text-sm text-muted-foreground">{user.email}</div>
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
```

### 3. 页面组件 (`src/app/`)

完整的页面级组件，组合多个业务组件。

```tsx
// ✅ 用户管理页面组件
export const UserManagementPage = () => {
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  
  const tableState = useTableState()
  const { data, loading, error, refetch } = useUserList({
    page: tableState.pagination.pageIndex + 1,
    size: tableState.pagination.pageSize,
    search: tableState.globalFilter,
    filters: tableState.columnFilters
  })
  
  const columns = useUserTableColumns({
    onEdit: (user) => {
      // 处理编辑
    },
    onDelete: (user) => {
      // 处理删除
    }
  })
  
  return (
    <div className="space-y-6">
      <PageHeader
        title="用户管理"
        description="管理系统用户和权限"
      >
        <Button onClick={() => setShowCreateModal(true)}>
          <Plus className="mr-2 h-4 w-4" />
          新建用户
        </Button>
      </PageHeader>
      
      <DataTable
        data={data?.content || []}
        columns={columns}
        loading={loading}
        error={error}
        pagination={{
          ...tableState.pagination,
          total: data?.total || 0
        }}
        onPaginationChange={tableState.setPagination}
        sorting={tableState.sorting}
        onSortingChange={tableState.setSorting}
        columnFilters={tableState.columnFilters}
        onColumnFiltersChange={tableState.setColumnFilters}
        globalFilter={tableState.globalFilter}
        onGlobalFilterChange={tableState.setGlobalFilter}
        rowSelection={selectedUsers}
        onRowSelectionChange={setSelectedUsers}
      />
      
      <CreateUserModal
        open={showCreateModal}
        onOpenChange={setShowCreateModal}
        onSuccess={() => {
          refetch()
          setShowCreateModal(false)
        }}
      />
    </div>
  )
}
```

## 组件开发模式

### 1. Compound Components 模式

用于创建灵活的组件 API：

```tsx
// ✅ 卡片组件的 Compound Components 实现
const Card = ({ children, className, ...props }) => (
  <div className={cn('rounded-lg border bg-card text-card-foreground shadow-sm', className)} {...props}>
    {children}
  </div>
)

const CardHeader = ({ children, className, ...props }) => (
  <div className={cn('flex flex-col space-y-1.5 p-6', className)} {...props}>
    {children}
  </div>
)

const CardTitle = ({ children, className, ...props }) => (
  <h3 className={cn('text-2xl font-semibold leading-none tracking-tight', className)} {...props}>
    {children}
  </h3>
)

const CardDescription = ({ children, className, ...props }) => (
  <p className={cn('text-sm text-muted-foreground', className)} {...props}>
    {children}
  </p>
)

const CardContent = ({ children, className, ...props }) => (
  <div className={cn('p-6 pt-0', className)} {...props}>
    {children}
  </div>
)

const CardFooter = ({ children, className, ...props }) => (
  <div className={cn('flex items-center p-6 pt-0', className)} {...props}>
    {children}
  </div>
)

// 导出组合
export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter }

// 使用示例
<Card>
  <CardHeader>
    <CardTitle>用户信息</CardTitle>
    <CardDescription>查看和编辑用户详细信息</CardDescription>
  </CardHeader>
  <CardContent>
    <UserForm />
  </CardContent>
  <CardFooter>
    <Button>保存</Button>
  </CardFooter>
</Card>
```

### 2. Render Props 模式

用于共享组件逻辑：

```tsx
// ✅ 数据获取组件的 Render Props 实现
interface DataFetcherProps<T> {
  url: string
  params?: Record<string, any>
  children: (state: {
    data: T | null
    loading: boolean
    error: string | null
    refetch: () => void
  }) => React.ReactNode
}

export const DataFetcher = <T,>({ url, params, children }: DataFetcherProps<T>) => {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await apiClient.get<T>({ url, params })
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取数据失败')
    } finally {
      setLoading(false)
    }
  }, [url, params])
  
  useEffect(() => {
    fetchData()
  }, [fetchData])
  
  return <>{children({ data, loading, error, refetch: fetchData })}</>
}

// 使用示例
<DataFetcher<User[]> url="/api/users">
  {({ data, loading, error, refetch }) => (
    <div>
      {loading && <Spinner />}
      {error && <ErrorMessage message={error} onRetry={refetch} />}
      {data && <UserList users={data} />}
    </div>
  )}
</DataFetcher>
```

### 3. Higher-Order Components (HOC) 模式

用于增强组件功能：

```tsx
// ✅ 权限控制 HOC
interface WithPermissionProps {
  permission: string
  fallback?: React.ReactNode
}

export const withPermission = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return ({ permission, fallback, ...props }: P & WithPermissionProps) => {
    const { hasPermission } = usePermissions()
    
    if (!hasPermission(permission)) {
      return fallback || <div>权限不足</div>
    }
    
    return <Component {...(props as P)} />
  }
}

// 使用示例
const ProtectedUserForm = withPermission(UserForm)

<ProtectedUserForm 
  permission="user.edit" 
  fallback={<div>您没有编辑用户的权限</div>}
  user={user}
  onSave={handleSave}
/>
```

## 表单组件开发

### 1. 受控表单组件

```tsx
// ✅ 受控输入组件
interface FormInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  label?: string
  error?: string
  description?: string
  required?: boolean
  onChange: (value: string) => void
  value: string
}

export const FormInput = React.forwardRef<HTMLInputElement, FormInputProps>(
  ({ label, error, description, required, onChange, value, className, ...props }, ref) => {
    const id = useId()
    
    return (
      <div className="space-y-2">
        {label && (
          <Label htmlFor={id} className={required ? "after:content-['*'] after:text-red-500" : ""}>
            {label}
          </Label>
        )}
        <Input
          id={id}
          ref={ref}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className={cn(error && 'border-red-500', className)}
          aria-invalid={!!error}
          aria-describedby={error ? `${id}-error` : description ? `${id}-description` : undefined}
          {...props}
        />
        {description && !error && (
          <p id={`${id}-description`} className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
        {error && (
          <p id={`${id}-error`} className="text-sm text-red-500">
            {error}
          </p>
        )}
      </div>
    )
  }
)
```

### 2. 复合表单组件

```tsx
// ✅ 地址输入组件
interface AddressInputProps {
  value: {
    province?: string
    city?: string
    district?: string
    detail?: string
  }
  onChange: (address: AddressInputProps['value']) => void
  error?: {
    province?: string
    city?: string
    district?: string
    detail?: string
  }
}

export const AddressInput: React.FC<AddressInputProps> = ({ value, onChange, error }) => {
  const { data: provinces } = useProvinces()
  const { data: cities } = useCities(value.province)
  const { data: districts } = useDistricts(value.city)
  
  const handleChange = (field: keyof AddressInputProps['value'], newValue: string) => {
    const newAddress = { ...value, [field]: newValue }
    
    // 清除下级选项
    if (field === 'province') {
      newAddress.city = ''
      newAddress.district = ''
    } else if (field === 'city') {
      newAddress.district = ''
    }
    
    onChange(newAddress)
  }
  
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <Select
          value={value.province}
          onValueChange={(val) => handleChange('province', val)}
        >
          <SelectTrigger className={error?.province ? 'border-red-500' : ''}>
            <SelectValue placeholder="选择省份" />
          </SelectTrigger>
          <SelectContent>
            {provinces?.map(province => (
              <SelectItem key={province.code} value={province.code}>
                {province.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select
          value={value.city}
          onValueChange={(val) => handleChange('city', val)}
          disabled={!value.province}
        >
          <SelectTrigger className={error?.city ? 'border-red-500' : ''}>
            <SelectValue placeholder="选择城市" />
          </SelectTrigger>
          <SelectContent>
            {cities?.map(city => (
              <SelectItem key={city.code} value={city.code}>
                {city.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select
          value={value.district}
          onValueChange={(val) => handleChange('district', val)}
          disabled={!value.city}
        >
          <SelectTrigger className={error?.district ? 'border-red-500' : ''}>
            <SelectValue placeholder="选择区县" />
          </SelectTrigger>
          <SelectContent>
            {districts?.map(district => (
              <SelectItem key={district.code} value={district.code}>
                {district.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <FormInput
        label="详细地址"
        value={value.detail || ''}
        onChange={(val) => handleChange('detail', val)}
        error={error?.detail}
        placeholder="请输入详细地址"
      />
    </div>
  )
}
```

## 数据展示组件

### 1. 数据表格组件

```tsx
// ✅ 可复用的数据表格组件
interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  loading?: boolean
  error?: string
  pagination?: {
    pageIndex: number
    pageSize: number
    total: number
  }
  onPaginationChange?: (pagination: { pageIndex: number; pageSize: number }) => void
  sorting?: SortingState
  onSortingChange?: (sorting: SortingState) => void
  columnFilters?: ColumnFiltersState
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void
  globalFilter?: string
  onGlobalFilterChange?: (filter: string) => void
  rowSelection?: string[]
  onRowSelectionChange?: (selection: string[]) => void
  onRowClick?: (row: T) => void
}

export const DataTable = <T,>({
  data,
  columns,
  loading = false,
  error,
  pagination,
  onPaginationChange,
  sorting = [],
  onSortingChange,
  columnFilters = [],
  onColumnFiltersChange,
  globalFilter = '',
  onGlobalFilterChange,
  rowSelection = [],
  onRowSelectionChange,
  onRowClick
}: DataTableProps<T>) => {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange,
    onColumnFiltersChange,
    onGlobalFilterChange,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination: pagination ? {
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize
      } : undefined
    }
  })
  
  if (error) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-center">
          <p className="text-red-500 mb-2">加载失败</p>
          <p className="text-sm text-muted-foreground">{error}</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="space-y-4">
      {/* 搜索和过滤 */}
      <div className="flex items-center justify-between">
        <Input
          placeholder="搜索..."
          value={globalFilter}
          onChange={(e) => onGlobalFilterChange?.(e.target.value)}
          className="max-w-sm"
        />
        <DataTableViewOptions table={table} />
      </div>
      
      {/* 表格 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder ? null : (
                      <div
                        className={cn(
                          'flex items-center space-x-2',
                          header.column.getCanSort() && 'cursor-pointer select-none'
                        )}
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getCanSort() && (
                          <Button variant="ghost" size="sm">
                            <ArrowUpDown className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <Spinner className="mx-auto" />
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={rowSelection.includes(row.id) && 'selected'}
                  className={onRowClick ? 'cursor-pointer' : ''}
                  onClick={() => onRowClick?.(row.original)}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  暂无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* 分页 */}
      {pagination && (
        <DataTablePagination
          table={table}
          pagination={pagination}
          onPaginationChange={onPaginationChange}
        />
      )}
    </div>
  )
}
```

## 组件测试指南

虽然项目暂未配置测试框架，但建议为组件编写测试：

```tsx
// ✅ 组件测试示例 (未来添加测试时参考)
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { UserSelector } from './UserSelector'

describe('UserSelector', () => {
  const mockUsers = [
    { id: '1', name: '张三', email: '<EMAIL>' },
    { id: '2', name: '李四', email: '<EMAIL>' }
  ]
  
  beforeEach(() => {
    // Mock API 调用
    jest.spyOn(userApi, 'list').mockResolvedValue(mockUsers)
  })
  
  it('应该渲染占位符文本', () => {
    render(<UserSelector onChange={jest.fn()} placeholder="选择用户" />)
    expect(screen.getByText('选择用户')).toBeInTheDocument()
  })
  
  it('应该在点击时打开下拉菜单', async () => {
    render(<UserSelector onChange={jest.fn()} />)
    
    fireEvent.click(screen.getByRole('combobox'))
    
    await waitFor(() => {
      expect(screen.getByText('张三')).toBeInTheDocument()
      expect(screen.getByText('李四')).toBeInTheDocument()
    })
  })
  
  it('应该在选择用户时调用 onChange', async () => {
    const onChange = jest.fn()
    render(<UserSelector onChange={onChange} />)
    
    fireEvent.click(screen.getByRole('combobox'))
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('张三'))
    })
    
    expect(onChange).toHaveBeenCalledWith(['1'])
  })
})
```

---

遵循这些组件开发指南将帮助团队构建一致、可复用和易维护的组件库。
