# 项目规范总结文档

## 项目基本信息

### 项目概览
- **项目名称**: Lux Customer Platform Backend
- **项目描述**: 基于Spring Boot和Kotlin的客户平台后端服务
- **技术栈**: Kotlin + Spring Boot + PostgreSQL + Redis
- **架构模式**: 分层架构 + DDD领域驱动设计
- **构建工具**: Gradle 8.14.3
- **JDK版本**: Java 21

### 核心功能模块
1. **用户认证模块**: JWT Token认证、Cookie会话管理
2. **客户账户管理**: 客户平台账户CRUD操作
3. **多租户支持**: 基于bizId的数据隔离
4. **安全防护**: API签名验证、CORS跨域支持

## 技术架构总览

### 分层架构设计
```
┌─────────────────────────────────────┐
│           Facade Layer              │  ← REST API接口层
│        (UserController)             │
├─────────────────────────────────────┤
│         Application Layer           │  ← 业务流程编排层
│   (UserLoginApplicationService)     │
├─────────────────────────────────────┤
│           Domain Layer              │  ← 核心业务逻辑层
│    (CustomerPlatformAccount)        │
├─────────────────────────────────────┤
│       Infrastructure Layer         │  ← 基础设施层
│  (Repository + Configuration)       │
└─────────────────────────────────────┘
```

### 核心技术组件
- **Web框架**: Spring Boot 3.5.3 + Spring MVC
- **数据访问**: Spring Data JPA + Hibernate
- **数据库**: PostgreSQL (主存储) + Redis (缓存/会话)
- **认证授权**: JWT Token + Cookie-based认证
- **序列化**: Jackson + Kotlin模块
- **日志框架**: kotlin-logging
- **连接池**: HikariCP

## 关键设计决策

### 1. 认证机制设计
- **JWT Token**: 使用Auth0 JWT库生成和验证Token
- **Cookie存储**: Token存储在HttpOnly Cookie中防止XSS
- **会话管理**: Redis存储用户会话，支持分布式部署
- **用户上下文**: FastThreadLocal管理线程本地用户信息

### 2. 多租户架构
- **数据隔离**: 通过bizId字段实现租户数据隔离
- **权限控制**: 基于用户上下文的bizId进行数据访问控制
- **查询重写**: 使用AuthRewriter自动添加租户条件

### 3. 数据访问设计
- **主键策略**: 雪花算法生成分布式唯一ID
- **审计字段**: 统一的创建时间、更新时间、操作人记录
- **软删除**: 通过状态字段实现逻辑删除
- **分页查询**: 统一的分页参数和响应格式

### 4. 异常处理策略
- **全局异常处理**: @RestControllerAdvice统一处理异常
- **分层异常**: 不同层次抛出不同类型的异常
- **错误响应**: 统一的错误响应格式和错误码

## 开发规范要点

### 1. 代码组织规范
```
io.cliveyou.luxcustomerplatformbackend/
├── application/          # 应用服务层 - 业务流程编排
├── common/              # 公共组件 - 枚举、异常、工具类
├── config/              # 配置类 - Spring配置、Web配置等
├── domain/              # 领域模型 - 实体类、业务逻辑
├── facade/              # 接口层 - Controller、DTO
└── infrastructure/      # 基础设施 - Repository、外部服务
```

### 2. 命名约定
- **类命名**: Controller、Service、Repository后缀
- **方法命名**: find/get(查询)、save/create(保存)、update(更新)、delete(删除)
- **变量命名**: 驼峰命名法，布尔变量is/has/can前缀
- **常量命名**: 大写字母+下划线

### 3. 注解使用规范
- **Spring注解**: @RestController、@Service、@Repository、@Component
- **JPA注解**: @Entity、@Table、@Column、@Id等
- **验证注解**: @Valid、@NotNull、@NotBlank等
- **事务注解**: @Transactional在Service层使用

## 配置管理规范

### 1. 多环境配置
- `application.yaml`: 基础配置和默认值
- `application-local.yaml`: 本地开发环境配置
- `application-test.yaml`: 测试环境配置
- `application-prod.yaml`: 生产环境配置

### 2. 配置优先级
1. 环境变量
2. 命令行参数
3. application-{profile}.yaml
4. application.yaml

### 3. 敏感信息管理
- 数据库密码通过环境变量配置
- JWT密钥外部化配置
- Redis密码环境变量管理

## 数据库设计规范

### 1. 表设计规范
- **命名**: 小写字母+下划线，复数形式
- **主键**: 统一使用Long类型ID，雪花算法生成
- **审计字段**: created_at、updated_at、created_by、updated_by
- **多租户**: 统一添加biz_id字段
- **软删除**: 使用status字段标记删除状态

### 2. 索引设计
- **主键索引**: 自动创建
- **业务索引**: 根据查询条件创建组合索引
- **唯一索引**: 业务唯一性约束
- **外键索引**: 关联查询优化

### 3. 数据类型规范
- **ID字段**: BIGINT
- **字符串**: TEXT类型，避免长度限制
- **时间戳**: BIGINT存储毫秒时间戳
- **枚举**: VARCHAR存储枚举名称
- **布尔**: BOOLEAN类型

## API设计规范

### 1. RESTful设计
- **资源命名**: 使用名词复数形式
- **HTTP方法**: GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- **状态码**: 200(成功)、400(参数错误)、401(未认证)、404(不存在)、500(服务器错误)

### 2. 请求响应格式
- **Content-Type**: application/json
- **字符编码**: UTF-8
- **时间格式**: ISO 8601格式或Unix时间戳
- **ID序列化**: Long类型序列化为字符串

### 3. 分页查询规范
- **参数**: page(页码)、size(每页大小)、sort(排序)
- **响应**: content(数据)、totalElements(总数)、totalPages(总页数)

## 安全设计规范

### 1. 认证授权
- **Token认证**: JWT Token存储在HttpOnly Cookie
- **会话管理**: Redis存储会话信息，支持单点登出
- **权限控制**: 基于用户上下文的权限验证

### 2. 数据安全
- **密码加密**: 使用BCrypt加密存储
- **SQL注入**: 使用JPA参数化查询
- **XSS防护**: HttpOnly Cookie + 输入验证

### 3. 接口安全
- **CORS配置**: 允许指定域名跨域访问
- **请求拦截**: API签名验证拦截器
- **白名单**: 登录、健康检查等接口白名单

## 部署运维规范

### 1. 构建部署
- **构建工具**: Gradle构建，生成可执行JAR
- **容器化**: Docker镜像部署，支持Kubernetes
- **环境隔离**: 通过Profile区分不同环境

### 2. 监控运维
- **健康检查**: Spring Boot Actuator健康检查
- **日志管理**: 结构化日志，支持日志聚合
- **性能监控**: JVM指标、数据库连接池监控

### 3. 备份恢复
- **数据备份**: 定期数据库备份
- **配置备份**: 配置文件版本管理
- **灾难恢复**: 数据恢复和服务恢复流程

## 后续AI开发建议

### 1. 理解项目结构
- 熟悉分层架构设计，严格按照层次职责开发
- 理解多租户架构，注意数据隔离
- 掌握认证授权机制，正确使用用户上下文

### 2. 遵循开发规范
- 按照命名规范编写代码
- 使用统一的异常处理机制
- 遵循RESTful API设计原则

### 3. 注意安全考虑
- 正确处理用户认证和授权
- 注意数据访问权限控制
- 遵循安全编码规范

### 4. 性能优化
- 合理使用缓存机制
- 优化数据库查询
- 注意连接池配置

这份规范文档为后续的AI开发会话提供了完整的项目上下文和开发指导，确保代码质量和架构一致性。
