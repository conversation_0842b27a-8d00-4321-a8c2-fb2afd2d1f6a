import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入翻译文件
import enTranslation from '@/locales/en.json';
import zhTranslation from '@/locales/zh.json';
import viTranslation from '@/locales/vi.json';

const resources = {
  en: {
    translation: enTranslation,
  },
  zh: {
    translation: zhTranslation,
  },
  vi: {
    translation: viTranslation,
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en', // 默认语言为英文
    debug: false,
    
    detection: {
      // 检测顺序：localStorage -> navigator -> fallback
      order: ['localStorage', 'navigator'],
      // localStorage的key
      lookupLocalStorage: 'i18nextLng',
      // 缓存用户语言选择
      caches: ['localStorage'],
    },

    interpolation: {
      escapeValue: false, // React已经默认转义
    },

    // 支持的语言
    supportedLngs: ['en', 'zh', 'vi'],
    
    // 非严格模式，允许回退到fallback语言
    nonExplicitSupportedLngs: true,
  });

export default i18n;
