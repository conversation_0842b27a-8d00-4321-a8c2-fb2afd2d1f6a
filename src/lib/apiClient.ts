import axios, { AxiosError, type AxiosRequestConfig, type AxiosResponse } from 'axios';

// 创建 axios 实例
const axiosInstance = axios.create({
    timeout: 500000,
    baseURL: import.meta.env.VITE_API_BASE_URL,
    headers: { 'Content-Type': 'application/json;charset=utf-8' },
    withCredentials: true, // 启用cookie支持
});


// 工具函数
function isServerSide(): boolean {
    return typeof window === 'undefined';
}

function handleAuthError(): void {
    // 清除cookie的操作由后端处理
    window.location.href = import.meta.env.VITE_LOGIN_URL || '/login';
}

// 请求拦截
axiosInstance.interceptors.request.use(
    (config) => {
        // cookie鉴权不需要在请求头中手动添加token
        return config;
    },
    (error) => {
        // 请求错误时做些什么
        return Promise.reject(error);
    },
);

// 响应拦截
axiosInstance.interceptors.response.use((res) => {
    if (res.data instanceof Blob) {
        return res;
    }
    // 检查响应的内容类型
    const contentType = res.headers['content-type'];
    if (contentType && contentType.includes('application/octet-stream')) {
        // 如果是二进制流数据，直接返回整个响应对象
        return res;
    }
    // 对于其他类型的响应，返回数据部分
    return res;
},
    (error: AxiosError<any>) => {
        if (error.response?.status === 401) {
            handleAuthError();
            return Promise.reject(new Error('登录过期，请重新登录'));
        }
        const { response, message } = error || {};

        let errMsg = '';
        try {
            errMsg = response?.data?.msg || message;
        } catch (error) {
            throw new Error(error as unknown as string);
        }
        if (errMsg === '') {
            errMsg = "system error";
        }
        // console.error(errMsg);
        return Promise.reject(new Error(errMsg));
    },
);

class APIClient {
    private makeRequest<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (isServerSide()) {
            return Promise.resolve({} as T);
        }
        return axiosInstance.request<any, AxiosResponse<T>>(config).then(res => res.data);
    }

    get<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'GET' });
    }

    post<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'POST' });
    }

    put<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'PUT' });
    }

    delete<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'DELETE' });
    }

    patch<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'PATCH' });
    }

    // 便捷方法：带 URL 和数据的请求
    getByUrl<T = any>(url: string, params?: any): Promise<T> {
        return this.get({ url, params });
    }

    postByUrl<T = any>(url: string, data?: any): Promise<T> {
        return this.post({ url, data });
    }

    putByUrl<T = any>(url: string, data?: any): Promise<T> {
        return this.put({ url, data });
    }

    deleteByUrl<T = any>(url: string): Promise<T> {
        return this.delete({ url });
    }

    patchByUrl<T = any>(url: string, data?: any): Promise<T> {
        return this.patch({ url, data });
    }
}

const apiClient = new APIClient();
export { apiClient };


