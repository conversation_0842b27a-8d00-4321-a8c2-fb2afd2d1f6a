"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { Eye, EyeOff, Mail, Lock } from "lucide-react"
import CommonButton from "@/components/ui/button/new-button"
import { toast } from "sonner"
import { useNavigate } from "react-router"
import { useRequest } from "ahooks";
import { UserApi } from "@/api/user/user-api"
import { useTranslation } from "react-i18next"
import LanguageSwitcher from "@/components/ui/language-switcher"

type LoginFormValues = {
    email: string;
    password: string;
    rememberMe: boolean;
}

export default function Login() {
    const { t } = useTranslation()
    const [showPassword, setShowPassword] = useState(false)
    const navigate = useNavigate()

    // Zod 表单验证模式 - 使用i18n
    const loginSchema = z.object({
        email: z
            .string()
            .min(1, t('auth.accountRequired')),
        password: z
            .string()
            .min(6, t('auth.passwordMinLength'))
            .max(50, "密码不能超过50个字符"),
        rememberMe: z.boolean(),
    })

    const form = useForm<LoginFormValues>({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: "",
            password: "",
            rememberMe: false,
        },
    })

    const { run: loginRequest, loading: loginLoading } = useRequest(UserApi.userLogin, {
        manual: true,
        onSuccess: () => {
            toast.success(t('auth.loginSuccess'))
            // cookie鉴权不需要手动保存token，由后端自动设置cookie
            // 跳转到主页面
            navigate('/dashboard/home')
        },
        onError: (error) => {
            toast.error(error.message || t('auth.loginFailed'))
        }
    })

    const onSubmit = async (values: LoginFormValues) => {
        const { rememberMe, ...loginData } = values
        await loginRequest(loginData)
    }

    return (
        <div className="min-h-screen flex">
            {/* 左侧图片区域 */}
            <div className="hidden lg:flex lg:w-1/2 relative">
                <img
                    src={"/login-left.jpg"}
                    alt="登录背景"
                    className="absolute inset-0 w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black/30" />
                <div className="relative z-10 flex flex-col justify-between h-full p-12">
                    {/* 顶部LOGO区域 */}
                    <div className="text-white">
                        <h1 className="text-3xl font-bold">{t('auth.systemName')}</h1>
                        <p className="text-lg opacity-90 mt-2">{t('auth.systemFullName')}</p>
                    </div>

                    {/* 底部标语区域 */}
                    <div className="text-white">
                        <h2 className="text-4xl font-bold mb-4">{t('auth.intelligentOrderManagement')}</h2>
                        <p className="text-xl opacity-90 max-w-md">
                            {t('auth.systemDescription')}
                        </p>
                    </div>
                </div>
            </div>

            {/* 右侧登录表单区域 */}
            <div className="flex-1 flex items-center justify-center p-8 bg-gray-50">
                <div className="w-full max-w-md">
                    {/* 语言切换器 */}
                    <div className="flex justify-end mb-4">
                        <LanguageSwitcher variant="outline" size="sm" />
                    </div>

                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('auth.loginTitle')}</h1>
                        <p className="text-gray-600">
                            {t('auth.loginSubtitle')}
                        </p>
                    </div>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            {/* 账号字段 */}
                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t('auth.account')}</FormLabel>
                                        <FormControl>
                                            <div className="relative">
                                                <Mail className="absolute left-3 top-2 h-4 w-4 text-gray-400" />
                                                <Input
                                                    placeholder={t('auth.accountPlaceholder')}
                                                    className="pl-10"
                                                    type="text"
                                                    autoComplete="text"
                                                    {...field}
                                                />
                                            </div>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* 密码字段 */}
                            <FormField
                                control={form.control}
                                name="password"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t('auth.password')}</FormLabel>
                                        <FormControl>
                                            <div className="relative">
                                                <Lock className="absolute left-3 top-2 h-4 w-4 text-gray-400" />
                                                <Input
                                                    placeholder={t('auth.passwordPlaceholder')}
                                                    className="pl-10 pr-10"
                                                    type={showPassword ? "text" : "password"}
                                                    autoComplete="current-password"
                                                    {...field}
                                                />
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                    onClick={() => setShowPassword(!showPassword)}
                                                    aria-label={showPassword ? "隐藏密码" : "显示密码"}
                                                >
                                                    {showPassword ? (
                                                        <EyeOff className="h-4 w-4 text-gray-400" />
                                                    ) : (
                                                        <Eye className="h-4 w-4 text-gray-400" />
                                                    )}
                                                </Button>
                                            </div>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* 记住我选择框 */}
                            <FormField
                                control={form.control}
                                name="rememberMe"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                        <FormControl>
                                            <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                        <div className="space-y-1 leading-none">
                                            <FormLabel className="text-sm font-normal cursor-pointer">
                                                {t('common.rememberMe')}
                                            </FormLabel>
                                        </div>
                                    </FormItem>
                                )}
                            />

                            {/* 提交按钮 */}
                            <CommonButton type="submit" className="w-full" disabled={loginLoading} loading={loginLoading}>
                                {loginLoading ? t('common.loading') : t('auth.loginButton')}
                            </CommonButton>

                            {/* 忘记密码链接 */}
                            {/* <div className="text-center">
                                <Button variant="link" className="text-sm text-gray-600">
                                    {t('common.forgotPassword')}
                                </Button>
                            </div> */}
                        </form>
                    </Form>
                </div>
            </div>
        </div>
    )
}