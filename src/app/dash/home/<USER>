import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import DateRangePicker from "@/components/dashboard/date-range-picker";
import OverviewStats from "@/components/dashboard/overview-stats";
import OrderTrendChart from "@/components/dashboard/order-trend-chart";
import StatusDistributionChart from "@/components/dashboard/status-distribution-chart";
import { dashboardApi } from "@/api/dash/dash-api";
import type { DashboardOverviewResponse, DashboardOrderTrendResponse } from "@/api/dashboard/dashboard-model";
import { toast } from "sonner";

export default function HomePage() {
    const { t } = useTranslation();

    // 状态管理
    const [loading, setLoading] = useState(true);
    const [trendData, setTrendData] = useState<DashboardOrderTrendResponse | null>(null);
    const [overviewData, setOverviewData] = useState<DashboardOverviewResponse | null>(null);
    const [dateRange, setDateRange] = useState<{ startDate?: number; endDate?: number }>({});
    const [initialized, setInitialized] = useState(false);

    // 处理日期范围变化
    const handleDateRangeChange = (startDate?: number, endDate?: number) => {
        console.log('HomePage handleDateRangeChange called:', { startDate, endDate });
        setDateRange({ startDate, endDate });
        if (!initialized) {
            setInitialized(true);
        }
    };

    // 获取数据
    const fetchData = async () => {
        try {
            setLoading(true);

            const request = {
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
            };

            // 添加调试日志
            console.log('Dashboard fetchData - dateRange:', dateRange);
            console.log('Dashboard fetchData - request:', request);

            // 并行获取概览和趋势数据
            const [overviewResponse, trendResponse] = await Promise.all([
                dashboardApi.getOverview(request),
                dashboardApi.getOrderTrend(request),
            ]);

            setOverviewData(overviewResponse);
            setTrendData(trendResponse);
        } catch (error) {
            console.error('Failed to fetch dashboard data:', error);
            toast.error(t('dashboard.messages.fetchDataFailed'));
        } finally {
            setLoading(false);
        }
    };

    // 监听日期范围变化，重新获取数据
    useEffect(() => {
        // 只有在初始化完成后才获取数据
        if (initialized) {
            fetchData();
        }
    }, [dateRange, initialized]);

    return (
        <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200">
            <div className="flex flex-col gap-4 h-full">
                <header className="px-6 py-4 space-y-1 flex flex-row items-center justify-between">
                    {/* left */}
                    <div>
                        <h1 className="text-[18px] font-medium">{t('dashboard.home')}</h1>
                        <h2 className="text-[13px] text-zinc-500">
                            <span className="text-zinc-500 text-[13px]">
                                {t('dashboard.homeDescription')}
                            </span>
                        </h2>
                    </div>
                    {/* right */}
                    <div className="pr-4">
                        <DateRangePicker onDateRangeChange={handleDateRangeChange} />
                    </div>
                </header>

                <Separator />

                <div className="flex-grow px-6 pb-6">
                    <Tabs defaultValue="overview" className="h-full flex flex-col">
                        <TabsList className="mb-6">
                            <TabsTrigger value="overview">{t('dashboard.tabs.overview')}</TabsTrigger>
                            <TabsTrigger value="trend">{t('dashboard.tabs.trend')}</TabsTrigger>
                        </TabsList>

                        <TabsContent value="overview" className="flex-grow space-y-6">
                            {/* 统计卡片 */}
                            {overviewData && (
                                <OverviewStats data={overviewData} loading={loading} />
                            )}

                            {/* 图表区域 */}
                            <div className="grid gap-6 lg:grid-cols-3">
                                {/* 趋势图表 */}
                                {trendData && (
                                    <div className="lg:col-span-2">
                                        <OrderTrendChart data={trendData} loading={loading} chartType="bar" />
                                    </div>
                                )}

                                {/* 状态分布图表 */}
                                {overviewData && (
                                    <div className="lg:col-span-1">
                                        <StatusDistributionChart
                                            data={overviewData.statusDistribution}
                                            loading={loading}
                                        />
                                    </div>
                                )}
                            </div>
                        </TabsContent>

                        <TabsContent value="trend" className="flex-grow">
                            {/* 详细趋势分析 */}
                            {trendData && (
                                <div className="grid gap-4">
                                    <OrderTrendChart data={trendData} loading={loading} chartType="line" />
                                </div>
                            )}
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </div>
    )
}