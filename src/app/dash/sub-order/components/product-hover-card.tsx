import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { CopyButton } from "@/components/ui/button/copy-button";
import type { SubOrderResponse } from "@/api/sub-order/sub-order-model";
import { useTranslation } from "react-i18next";

interface ProductHoverCardProps {
    product: SubOrderResponse['product'];
    children: React.ReactNode;
}

export function ProductHoverCard({ product, children }: ProductHoverCardProps) {
    const { t } = useTranslation();

    return (
        <HoverCard openDelay={200} closeDelay={150}>
            <HoverCardTrigger asChild>
                {children}
            </HoverCardTrigger>
            <HoverCardContent
                className="w-80 p-0 overflow-hidden bg-white border-gray-300 shadow-xl"
                side="right"
                align="start"
            >
                <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                    <h3 className="font-semibold text-gray-900 text-sm">{t('subOrder.productDetails.title')}</h3>
                </div>

                <div className="p-3 space-y-2">
                    {/* 商品名称 */}
                    <div className="flex items-center justify-between gap-2">
                        <div className="text-sm font-medium text-gray-900 flex-1 min-w-0">
                            {product.name || product.title || t('subOrder.productDetails.unknownProduct')}
                        </div>
                        <CopyButton
                            text={product.name || product.title || t('subOrder.productDetails.unknownProduct')}
                            size="xs"
                            className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                        />
                    </div>

                    {/* SPU */}
                    {product.spu && (
                        <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-500">{t('subOrder.productDetails.spu')}</span>
                            <span className="text-xs font-mono bg-gray-100 px-2 py-1 rounded border flex-1">
                                {product.spu}
                            </span>
                            <CopyButton
                                text={product.spu}
                                size="xs"
                                className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                            />
                        </div>
                    )}

                    {/* 规格信息 */}
                    {(product.size || product.color) && (
                        <div className="flex gap-3">
                            {product.size && (
                                <div className="flex items-center gap-1">
                                    <span className="text-xs text-gray-500">{t('subOrder.productDetails.size')}</span>
                                    <span className="text-xs bg-gray-200 text-gray-800 px-2 py-1 rounded">
                                        {product.size}
                                    </span>
                                </div>
                            )}
                            {product.color && (
                                <div className="flex items-center gap-1">
                                    <span className="text-xs text-gray-500">{t('subOrder.productDetails.color')}</span>
                                    <span className="text-xs bg-gray-200 text-gray-800 px-2 py-1 rounded">
                                        {product.color}
                                    </span>
                                </div>
                            )}
                        </div>
                    )}

                    {/* 数量 */}
                    <div className="pt-1 border-t border-gray-200">
                        <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-500">{t('subOrder.productDetails.quantity')}</span>
                            <span className="text-sm font-semibold text-gray-900">
                                {product.qty} {t('subOrder.productDetails.pieces')}
                            </span>
                        </div>
                    </div>
                </div>
            </HoverCardContent>
        </HoverCard>
    );
} 