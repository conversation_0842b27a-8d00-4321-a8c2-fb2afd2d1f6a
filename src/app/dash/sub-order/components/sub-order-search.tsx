import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CalendarIcon } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { enUS } from "date-fns/locale";
import { useTranslation } from "react-i18next";
import useSearchParamsManager from "@/hooks/use-url-param";
import { DataTableUrlFilter } from "@/components/select/data-table-column-filter";
import { getSubOrderStatusOptions } from "@/utils/sub-order-status";

interface SubOrderSearchProps {
  loading?: boolean;
}

export interface SubOrderSearchParams {
  orderNo?: string;
  customerOrderNo?: string;
  start?: number;
  end?: number;
}

export function SubOrderSearch({ }: SubOrderSearchProps) {
  const { t } = useTranslation();
  const { getParam, addParam } = useSearchParamsManager();

  const endTime = getParam('end');
  const startTime = getParam('start');
  const customerOrderNo = getParam('customerOrderNo') || '';
  const startDate = startTime ? new Date(parseInt(startTime)) : undefined;
  const endDate = endTime ? new Date(parseInt(endTime)) : undefined;

  const handleSearch = () => {
    // 搜索时不需要做任何操作，因为参数已经在 URL 中
  };


  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleCustomerOrderNoChange = (value: string) => {
    addParam('customerOrderNo', value);
  };

  const handleStartDateChange = (date: Date | undefined) => {
    if (date) {
      addParam('start', date.getTime().toString());
    } else {
      addParam('start', '');
    }
  };

  const handleEndDateChange = (date: Date | undefined) => {
    if (date) {
      const endDateTime = new Date(date);
      endDateTime.setHours(23, 59, 59, 999);
      addParam('end', endDateTime.getTime().toString());
    } else {
      addParam('end', '');
    }
  };

  return (
    <div className="px-3 py-1">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-3 items-end">
        <div className="space-y-1">
          <Label htmlFor="customerOrderNo" className="text-xs font-medium">{t('subOrder.search.customerOrderNo')}</Label>
          <Input
            id="customerOrderNo"
            placeholder={t('subOrder.search.customerOrderNoPlaceholder')}
            value={customerOrderNo}
            onChange={(e) => handleCustomerOrderNoChange(e.target.value)}
            onKeyPress={handleKeyPress}
            className="h-8 text-xs"
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="status" className="text-xs font-medium">{t('subOrder.search.status')}</Label>
          <DataTableUrlFilter
            paramKey="status"
            title={t('subOrder.search.status')}
            options={getSubOrderStatusOptions(t)}
          />
        </div>

        <div className="space-y-1">
          <Label className="text-xs font-medium">{t('subOrder.search.startTime')}</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal h-8 text-xs px-2",
                  !startDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-1 h-3 w-3" />
                {startDate ? format(startDate, "MM-dd", { locale: enUS }) : t('subOrder.search.selectStartTime')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={handleStartDateChange}
                initialFocus
                locale={enUS}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* 结束时间 */}
        <div className="space-y-1">
          <Label className="text-xs font-medium">{t('subOrder.search.endTime')}</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal h-8 text-xs px-2",
                  !endDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-1 h-3 w-3" />
                {endDate ? format(endDate, "MM-dd", { locale: enUS }) : t('subOrder.search.selectEndTime')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={handleEndDateChange}
                initialFocus
                locale={enUS}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );
}
