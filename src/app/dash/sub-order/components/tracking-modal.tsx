import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { CheckIcon, PackageIcon, TruckIcon, MapPinIcon, ClockIcon, ExternalLinkIcon, PhoneIcon, GlobeIcon } from "lucide-react";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { trackingApi } from "@/api/tracking/tracking-api";
import type { TrackingInfoResponse } from "@/api/tracking/tracking-model";
import {
  Timeline,
  TimelineContent,
  TimelineDate,
  TimelineHeader,
  TimelineIndicator,
  TimelineItem,
  TimelineSeparator,
  TimelineTitle,
} from "@/components/ui/timeline";

interface TrackingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  subOrderId: number;
  customerOrderNo: string;
}

export function TrackingModal({ open, onOpenChange, subOrderId, customerOrderNo }: TrackingModalProps) {
  const { t } = useTranslation();
  const [trackingInfo, setTrackingInfo] = useState<TrackingInfoResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取轨迹信息
  const fetchTrackingInfo = async () => {
    if (!open || !subOrderId) return;

    setLoading(true);
    setError(null);

    try {
      const data = await trackingApi.getSubOrderTracking(subOrderId);
      setTrackingInfo(data);
      if (!data) {
        setError(t('tracking.noTrackingInfo'));
      }
    } catch (err) {
      setError(t('tracking.fetchError'));
      console.error('Failed to fetch tracking info:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrackingInfo();
  }, [open, subOrderId]);

  // 格式化时间
  const formatDateTime = (timestamp: string) => {
    return dayjs(parseInt(timestamp)).format('YYYY-MM-DD HH:mm');
  };

  const formatDateOnly = (timestamp: string) => {
    return dayjs(parseInt(timestamp)).format('MM-DD');
  };

  const formatTimeOnly = (timestamp: string) => {
    return dayjs(parseInt(timestamp)).format('HH:mm');
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DELIVERED':
        return {
          bg: 'bg-emerald-50',
          text: 'text-emerald-700',
          border: 'border-emerald-200',
          indicator: 'bg-emerald-500'
        };
      case 'IN_TRANSIT':
      case 'PICKED_UP':
      case 'OUT_FOR_DELIVERY':
        return {
          bg: 'bg-blue-50',
          text: 'text-blue-700',
          border: 'border-blue-200',
          indicator: 'bg-blue-500'
        };
      case 'EXCEPTION':
      case 'DELIVERY_FAILED':
      case 'RETURNED':
      case 'CANCELLED':
        return {
          bg: 'bg-red-50',
          text: 'text-red-700',
          border: 'border-red-200',
          indicator: 'bg-red-500'
        };
      case 'NOT_FOUND':
      case 'UNKNOWN':
        return {
          bg: 'bg-slate-50',
          text: 'text-slate-700',
          border: 'border-slate-200',
          indicator: 'bg-slate-400'
        };
      default:
        return {
          bg: 'bg-amber-50',
          text: 'text-amber-700',
          border: 'border-amber-200',
          indicator: 'bg-amber-500'
        };
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DELIVERED':
        return <CheckIcon className="h-4 w-4" />;
      case 'PICKED_UP':
        return <PackageIcon className="h-4 w-4" />;
      case 'IN_TRANSIT':
      case 'OUT_FOR_DELIVERY':
        return <TruckIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[90%] lg:max-w-[1000px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TruckIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <div>{t('tracking.title')}</div>
              <p className="text-sm font-normal text-muted-foreground">
                {t('tracking.orderNo')}: {customerOrderNo}
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {loading && (
            <div className="flex items-center justify-center py-16">
              <div className="flex flex-col items-center gap-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <div className="text-sm text-muted-foreground">{t('common.loading')}</div>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="text-red-600 text-lg mb-2">⚠️</div>
                <div className="text-sm text-red-600">{error}</div>
              </div>
            </div>
          )}

          {trackingInfo && (
            <div className="space-y-8">
              {/* 当前状态卡片 */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">{t('tracking.currentStatus')}</h3>
                  <Badge className={`${getStatusColor(trackingInfo.currentStatus).bg} ${getStatusColor(trackingInfo.currentStatus).text} ${getStatusColor(trackingInfo.currentStatus).border} border`}>
                    {getStatusIcon(trackingInfo.currentStatus)}
                    <span className="ml-2 font-medium">{t(`tracking.status.${trackingInfo.currentStatus}`)}</span>
                  </Badge>
                </div>

                <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">{t('tracking.waybillNo')}</div>
                    <div className="text-sm font-mono bg-white px-2 py-1 rounded border">{trackingInfo.waybillNo}</div>
                  </div>
                  {trackingInfo.trackingNumber && (
                    <div className="space-y-1">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">{t('tracking.trackingNumber')}</div>
                      <div className="text-sm font-mono bg-white px-2 py-1 rounded border">{trackingInfo.trackingNumber}</div>
                    </div>
                  )}
                  <div className="space-y-1">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">{t('tracking.channel')}</div>
                    <div className="text-sm bg-white px-2 py-1 rounded border">{trackingInfo.channel}</div>
                  </div>
                  {trackingInfo.deliveryDays && (
                    <div className="space-y-1">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">{t('tracking.deliveryDays')}</div>
                      <div className="text-sm bg-white px-2 py-1 rounded border">{trackingInfo.deliveryDays} {t('tracking.days')}</div>
                    </div>
                  )}
                </div>

                {trackingInfo.lastUpdatedAt && (
                  <div className="mt-4 pt-4 border-t border-blue-200">
                    <div className="text-xs text-blue-600">
                      {t('tracking.lastUpdated')}: {formatDateTime(trackingInfo.lastUpdatedAt)}
                    </div>
                  </div>
                )}
              </div>

              {/* 末端服务商信息 */}
              {trackingInfo.lastMileProvider && (
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <TruckIcon className="h-5 w-5 text-gray-600" />
                    {t('tracking.lastMileProvider')}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-1">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">{t('tracking.providerName')}</div>
                      <div className="text-sm font-medium">{trackingInfo.lastMileProvider.name}</div>
                    </div>
                    {trackingInfo.lastMileProvider.telephone && (
                      <div className="space-y-1">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">{t('tracking.telephone')}</div>
                        <div className="flex items-center gap-2">
                          <PhoneIcon className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">{trackingInfo.lastMileProvider.telephone}</span>
                        </div>
                      </div>
                    )}
                    {trackingInfo.lastMileProvider.website && (
                      <div className="space-y-1">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">{t('tracking.website')}</div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-auto p-2 text-sm hover:bg-blue-50"
                          onClick={() => window.open(trackingInfo.lastMileProvider!.website, '_blank')}
                        >
                          <GlobeIcon className="h-4 w-4 mr-2" />
                          {t('tracking.visitWebsite')}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 轨迹事件时间线 */}
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
                <div className="p-6 border-b border-gray-100">
                  <h4 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <ClockIcon className="h-5 w-5 text-gray-600" />
                    {t('tracking.trackingEvents')}
                  </h4>
                </div>

                <div className="p-6">
                  {trackingInfo.trackingEvents.length > 0 ? (
                    <Timeline defaultValue={trackingInfo.trackingEvents.length}>
                      {trackingInfo.trackingEvents.map((event, index) => {
                        const isLatest = index === 0;
                        const step = trackingInfo.trackingEvents.length - index;

                        return (
                          <TimelineItem
                            key={index}
                            step={step}
                            className="group-data-[orientation=vertical]/timeline:ms-10"
                          >
                            <TimelineHeader>
                              <TimelineSeparator className="group-data-[orientation=vertical]/timeline:-left-7 group-data-[orientation=vertical]/timeline:h-[calc(100%-1.5rem-0.25rem)] group-data-[orientation=vertical]/timeline:translate-y-6.5" />
                              <TimelineDate className="text-right text-xs text-gray-500 flex-shrink-0 min-w-[80px]">
                                <div className="font-medium">{formatDateOnly(event.eventTime)}</div>
                                <div className="text-gray-400">{formatTimeOnly(event.eventTime)}</div>
                              </TimelineDate>
                              <TimelineTitle>
                                <div className="flex items-center gap-2 mb-2">
                                  <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                                    {getStatusIcon(event.status)}
                                    <span className="ml-1">{t(`tracking.status.${event.status}`)}</span>
                                  </Badge>
                                  {event.isLastMileEvent && (
                                    <Badge variant="secondary" className="text-xs">
                                      {t('tracking.lastMileEvent')}
                                    </Badge>
                                  )}
                                  {isLatest && (
                                    <Badge className="text-xs bg-blue-500 text-white">
                                      {t('tracking.latest')}
                                    </Badge>
                                  )}
                                </div>
                              </TimelineTitle>
                              <TimelineIndicator className={`group-data-completed/timeline-item:bg-primary group-data-completed/timeline-item:text-primary-foreground flex size-6 items-center justify-center group-data-completed/timeline-item:border-none group-data-[orientation=vertical]/timeline:-left-7 ${isLatest ? 'bg-blue-500 ring-4 ring-blue-100' : ''}`}>
                                <CheckIcon
                                  className="group-not-data-completed/timeline-item:hidden"
                                  size={16}
                                />
                              </TimelineIndicator>
                            </TimelineHeader>
                            <TimelineContent>
                              <p className="text-sm text-gray-900 mb-2 leading-relaxed">{event.description}</p>

                              {(event.location || event.locationDetail) && (
                                <div className="flex items-start gap-1 text-xs text-gray-500">
                                  <MapPinIcon className="h-3 w-3 mt-0.5 flex-shrink-0" />
                                  <span className="leading-relaxed">
                                    {event.locationDetail
                                      ? [event.locationDetail.country, event.locationDetail.province, event.locationDetail.city, event.locationDetail.address]
                                        .filter(Boolean).join(' • ')
                                      : event.location
                                    }
                                  </span>
                                </div>
                              )}
                            </TimelineContent>
                          </TimelineItem>
                        );
                      })}
                    </Timeline>
                  ) : (
                    <div className="text-center py-12 text-muted-foreground">
                      <ClockIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                      <div>{t('tracking.noEvents')}</div>
                    </div>
                  )}
                </div>
              </div>

              {/* 妥投证明链接 */}
              {trackingInfo.podLinks.length > 0 && (
                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <ExternalLinkIcon className="h-5 w-5 text-gray-600" />
                    {t('tracking.podLinks')}
                  </h4>
                  <div className="flex flex-wrap gap-3">
                    {trackingInfo.podLinks.map((link, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        className="hover:bg-blue-50 hover:border-blue-200"
                        onClick={() => window.open(link, '_blank')}
                      >
                        <ExternalLinkIcon className="h-4 w-4 mr-2" />
                        {t('tracking.viewPod')} {index + 1}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
