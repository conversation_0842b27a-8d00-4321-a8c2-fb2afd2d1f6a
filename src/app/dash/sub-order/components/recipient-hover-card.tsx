import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { CopyButton } from "@/components/ui/button/copy-button";
import type { SubOrderResponse } from "@/api/sub-order/sub-order-model";
import { useTranslation } from "react-i18next";

interface RecipientHoverCardProps {
    recipient: SubOrderResponse['recipient'];
    children: React.ReactNode;
}

export function RecipientHoverCard({ recipient, children }: RecipientHoverCardProps) {
    const { t } = useTranslation();

    return (
        <HoverCard openDelay={200} closeDelay={150}>
            <HoverCardTrigger asChild>
                {children}
            </HoverCardTrigger>
            <HoverCardContent
                className="w-80 p-0 overflow-hidden bg-white border-gray-300 shadow-xl"
                side="right"
                align="start"
            >
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="font-semibold text-gray-900 text-sm mb-1">{t('subOrder.recipientDetails.title')}</h3>
                    <p className="text-xs text-gray-500">{t('subOrder.recipientDetails.subtitle')}</p>
                </div>

                <div className="p-4 space-y-3">
                    {/* 收件人姓名 */}
                    <div>
                        <div className="flex items-center justify-between">
                            <div className="text-sm font-medium text-gray-900">
                                {recipient.receiverName || recipient.userName || t('subOrder.recipientDetails.unknownRecipient')}
                            </div>
                            <CopyButton
                                text={recipient.receiverName || recipient.userName || t('subOrder.recipientDetails.unknownRecipient')}
                                size="xs"
                                className="text-gray-400 hover:text-gray-600"
                            />
                        </div>
                        {recipient.receiverName && recipient.userName && recipient.receiverName !== recipient.userName && (
                            <div className="text-xs text-gray-500 mt-1">
                                {t('subOrder.recipientDetails.username')} {recipient.userName}
                            </div>
                        )}
                    </div>

                    {/* 联系方式 */}
                    {(recipient.phone || recipient.email) && (
                        <div className="space-y-2">
                            {recipient.phone && (
                                <div>
                                    <div className="text-xs text-gray-500">{t('subOrder.recipientDetails.phone')}</div>
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm font-medium text-gray-900 font-mono">
                                            {recipient.phone}
                                        </div>
                                        <CopyButton
                                            text={recipient.phone}
                                            size="xs"
                                            className="text-gray-400 hover:text-gray-600"
                                        />
                                    </div>
                                </div>
                            )}

                            {recipient.email && (
                                <div>
                                    <div className="text-xs text-gray-500">{t('subOrder.recipientDetails.email')}</div>
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm font-medium text-gray-900 break-all mr-2">
                                            {recipient.email}
                                        </div>
                                        <CopyButton
                                            text={recipient.email}
                                            size="xs"
                                            className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    {/* 地址信息 */}
                    <div className="pt-2 border-t border-gray-200">
                        <div className="text-xs text-gray-500 mb-2">{t('subOrder.recipientDetails.shippingAddress')}</div>

                        {/* 国家/地区/城市 */}
                        {recipient.country && (
                            <div className="flex items-center gap-2 mb-2">
                                <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded border">
                                    {recipient.country}
                                </span>
                                {recipient.state && (
                                    <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                                        {recipient.state}
                                    </span>
                                )}
                                {recipient.city && (
                                    <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                                        {recipient.city}
                                    </span>
                                )}
                            </div>
                        )}

                        {/* 详细地址 */}
                        {(recipient.address1 || recipient.address2) && (
                            <div className="space-y-1">
                                {recipient.address1 && (
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-gray-900 mr-2">
                                            {recipient.address1}
                                        </div>
                                        <CopyButton
                                            text={recipient.address1}
                                            size="xs"
                                            className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                                        />
                                    </div>
                                )}
                                {recipient.address2 && (
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-gray-600 mr-2">
                                            {recipient.address2}
                                        </div>
                                        <CopyButton
                                            text={recipient.address2}
                                            size="xs"
                                            className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                                        />
                                    </div>
                                )}
                            </div>
                        )}

                        {/* 邮政编码 */}
                        {recipient.postcode && (
                            <div className="flex items-center justify-between mt-2">
                                <div className="flex items-center gap-2">
                                    <span className="text-xs text-gray-500">{t('subOrder.recipientDetails.postalCode')}</span>
                                    <span className="text-xs font-mono bg-gray-100 text-gray-800 px-2 py-1 rounded border">
                                        {recipient.postcode}
                                    </span>
                                </div>
                                <CopyButton
                                    text={recipient.postcode}
                                    size="xs"
                                    className="text-gray-400 hover:text-gray-600"
                                />
                            </div>
                        )}
                    </div>
                </div>
            </HoverCardContent>
        </HoverCard>
    );
} 