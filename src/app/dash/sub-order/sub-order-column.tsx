import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { SubOrderResponse } from "@/api/sub-order/sub-order-model";
import { ProductHoverCard } from "./components/product-hover-card";
import { RecipientHoverCard } from "./components/recipient-hover-card";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { CopyButton } from "@/components/ui/button/copy-button";
import { getSubOrderStatusLabel } from "@/utils/sub-order-status";
import { TruckIcon, ExternalLinkIcon } from "lucide-react";


// 格式化时间
const formatDateTime = (timestamp: string | number) => {
  return dayjs(typeof timestamp === 'string' ? parseInt(timestamp) : timestamp).format('YYYY-MM-DD HH:mm');
};

// 状态颜色映射 - 按订单生命周期阶段分组
const getStatusDotColor = (status: string) => {
  switch (status) {
    // 初始状态 - 蓝色系
    case 'CREATED':
      return 'bg-blue-500';

    // 处理中状态 - 黄色/橙色系
    case 'PROCESSING':
    case 'SUPPLIER_MATCHED':
    case 'FAILED':
      return 'bg-yellow-500';

    case 'SPLIT':
      return 'bg-orange-500';

    // 物流跟踪状态 - 从浅蓝到深蓝渐进
    case 'TRACKING_PENDING':
    case 'TRACKING_NOT_FOUND':
    case 'TRACKING_PRE_ADVICE_RECEIVED':
      return 'bg-slate-500';

    case 'TRACKING_PICKED_UP':
      return 'bg-indigo-500';

    case 'TRACKING_IN_TRANSIT':
    case 'TRACKING_ARRIVED_DESTINATION_COUNTRY':
    case 'TRACKING_IN_CUSTOMS':
    case 'TRACKING_CUSTOMS_CLEARED':
      return 'bg-blue-500';

    case 'TRACKING_ARRIVED_FOR_PICKUP':
    case 'TRACKING_OUT_FOR_DELIVERY':
      return 'bg-cyan-500';

    // 成功状态 - 绿色系
    case 'COMPLETED':
    case 'TRACKING_DELIVERED':
      return 'bg-green-500';

    // 失败/异常状态 - 红色系
    case 'CANCELLED':
    case 'TRACKING_CANCELLED':
      return 'bg-red-500';

    case 'TRACKING_DELIVERY_FAILED':
    case 'TRACKING_EXCEPTION':
    case 'TRACKING_RETURNED':
    case 'TRACKING_UNKNOWN':
      return 'bg-rose-500';

    // 默认状态和兼容性处理
    default:
      // 处理以 TRACKING_DELIVERED 开头的状态
      if (status.startsWith('TRACKING_DELIVERED')) {
        return 'bg-green-500';
      }
      // 处理其他 TRACKING_ 开头的状态
      else if (status.startsWith('TRACKING_')) {
        return 'bg-blue-500';
      }
      // 未知状态
      return 'bg-gray-500';
  }
};

export const subOrderColumns: ColumnDef<SubOrderResponse>[] = [
  {
    id: "customerOrderNo",
    accessorKey: "customerOrderNo",
    header: () => {
      const { t } = useTranslation();
      return t('subOrder.table.columns.customerOrderNo');
    },
    cell: ({ row }) => {
      const customerOrderNo = row.getValue("customerOrderNo") as string;
      return (
        <div className="flex items-center gap-2 w-[120px] max-w-[120px]">
          <span className="font-mono text-xs truncate flex-1">{customerOrderNo || '-'}</span>
          {customerOrderNo && (
            <div className="flex-shrink-0">
              <CopyButton text={customerOrderNo} />
            </div>
          )}
        </div>
      );
    },
    meta: {
      title: "客户订单号",
      enableSortingIcon: true
    }
  },
  {
    id: "recipient",
    accessorKey: "recipient",
    header: () => {
      const { t } = useTranslation();
      return t('subOrder.table.columns.recipient');
    },
    cell: ({ row }) => {
      const { t } = useTranslation();
      const recipient = row.getValue("recipient") as SubOrderResponse['recipient'];
      return (
        <RecipientHoverCard recipient={recipient}>
          <div className="w-[180px] max-w-[180px] cursor-pointer hover:bg-gray-50 rounded-md p-2 transition-colors duration-200 border border-transparent hover:border-gray-200">
            <div className="flex flex-col gap-1">
              <div className="font-medium text-gray-900 truncate text-sm">
                {recipient.receiverName || recipient.userName || t('subOrder.table.recipient.unknownRecipient')}
              </div>
              <div className="text-xs text-gray-500 truncate">
                {recipient.country && (
                  <span>{recipient.country}</span>
                )}
                {recipient.state && (
                  <span>{recipient.country ? ', ' : ''}{recipient.state}</span>
                )}
                {recipient.city && (
                  <span>{(recipient.country || recipient.state) ? ', ' : ''}{recipient.city}</span>
                )}
              </div>
            </div>
          </div>
        </RecipientHoverCard>
      );
    },
    meta: {
      title: "收件人信息"
    }
  },
  {
    id: "product",
    accessorKey: "product",
    header: () => {
      const { t } = useTranslation();
      return t('subOrder.table.columns.product');
    },
    cell: ({ row }) => {
      const { t } = useTranslation();
      const product = row.getValue("product") as SubOrderResponse['product'];
      return (
        <ProductHoverCard product={product}>
          <div className="w-[200px] max-w-[200px] cursor-pointer hover:bg-gray-50 rounded-md p-2 transition-colors duration-200 border border-transparent hover:border-gray-200">
            <div className="flex flex-col gap-1">
              <div className="font-medium text-gray-900 truncate text-sm">
                {product.name || product.title || t('subOrder.table.product.unknownProduct')}
              </div>
              <div className="text-xs text-gray-500">
                <span>{t('subOrder.table.product.quantity')}: {product.qty}</span>
              </div>
            </div>
          </div>
        </ProductHoverCard>
      );
    },
    meta: {
      title: "商品信息"
    }
  },

  // {
  //   id: "shipping",
  //   accessorKey: "shipping",
  //   header: () => {
  //     const { t } = useTranslation();
  //     return t('subOrder.table.columns.shipping');
  //   },
  //   cell: ({ }) => {
  //     return (
  //       <div className="w-[120px] max-w-[120px]">
  //         <span className="text-sm truncate block">{'-'}</span>
  //       </div>
  //     );
  //   },
  //   meta: {
  //     title: "物流信息订单号"
  //   }
  // },
  {
    id: "status",
    accessorKey: "status",
    header: () => {
      const { t } = useTranslation();
      return t('subOrder.table.columns.status');
    },
    cell: ({ row }) => {
      const { t } = useTranslation();
      const status = row.getValue("status") as string;
      const displayStatus = (row.original as any).displayStatus || status;

      // 使用 displayStatus 来获取标签和颜色
      const label = displayStatus === 'PROCESSING'
        ? t('subOrder.status.PROCESSING')
        : getSubOrderStatusLabel(status as any, t);

      const colorStatus = displayStatus === 'PROCESSING' ? 'PROCESSING' : status;
      const dotColor = getStatusDotColor(colorStatus);

      return (
        <div className="w-[140px] max-w-[140px]">
          <Badge className="bg-[#FAFAFA] text-[#707070] border-0 rounded-md px-3 py-1.5 font-medium max-w-full">
            <div className="flex items-center gap-2 truncate">
              <div className={`w-2 h-2 rounded-xs flex-shrink-0 ${dotColor}`}></div>
              <span className="truncate">{label}</span>
            </div>
          </Badge>
        </div>
      );
    },
    meta: {
      title: "订单状态"
    }
  },
  {
    id: "createdAt",
    accessorKey: "createdAt",
    header: () => {
      const { t } = useTranslation();
      return t('subOrder.table.columns.createdAt');
    },
    cell: ({ row }) => {
      const createdAt = row.getValue("createdAt") as number;
      return (
        <div className="w-[140px] max-w-[140px]">
          <span className="text-sm truncate block">
            {formatDateTime(createdAt)}
          </span>
        </div>
      );
    },
    meta: {
      title: "创建时间",
      enableSortingIcon: true,
      defaultSortDesc: true
    }
  },
  {
    id: "actions",
    header: () => {
      const { t } = useTranslation();
      return t('subOrder.table.columns.actions');
    },
    cell: ({ row }) => {
      const { t } = useTranslation();
      const subOrder = row.original;
      return (
        <div className="w-[180px] max-w-[180px]">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // 这里需要在父组件中处理，暂时使用window事件
                window.dispatchEvent(new CustomEvent('openTrackingModal', {
                  detail: {
                    subOrderId: subOrder.id,
                    customerOrderNo: subOrder.customerOrderNo
                  }
                }));
              }}
              className="h-8 px-2 text-xs"
            >
              <TruckIcon className="h-3 w-3" />
              <span className="ml-1 hidden sm:inline">{t('subOrder.table.actions.viewTracking')}</span>
              <span className="ml-1 sm:hidden">轨迹</span>
            </Button>
            {subOrder.shipping.waybillLabelUrl && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(subOrder.shipping.waybillLabelUrl, '_blank')}
                className="h-8 px-2 text-xs"
              >
                <ExternalLinkIcon className="h-3 w-3" />
                <span className="ml-1 hidden sm:inline">{t('subOrder.table.actions.viewLabel')}</span>
                <span className="ml-1 sm:hidden">标签</span>
              </Button>
            )}
          </div>
        </div>
      );
    },
    meta: {
      title: "操作"
    }
  }
];
