package io.cliveyou.luxcustomerplatformbackend.application

import io.cliveyou.luxcustomerplatformbackend.facade.tracking.response.TrackingInfoResponse
import io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa.TrackingInfoRepository
import org.springframework.stereotype.Service

/**
 * 轨迹信息应用服务
 * 处理轨迹相关的业务逻辑
 */
@Service
class TrackingApplicationService(
    private val trackingInfoRepository: TrackingInfoRepository,
) {

    /**
     * 根据订单号查询轨迹信息
     * 
     * @param orderNo 订单号
     * @return 轨迹信息响应DTO，如果未找到则返回null
     */
    fun getTrackingByOrderNo(orderNo: String): TrackingInfoResponse? {
        val trackingInfo = trackingInfoRepository.findByOrderNo(orderNo)
        return trackingInfo?.let { TrackingInfoResponse.from(it) }
    }
}
