package io.cliveyou.luxcustomerplatformbackend.application

import io.cliveyou.luxcustomerplatformbackend.common.exception.CognitionException
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.convertToSpecWithPermissions
import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrder
import io.cliveyou.luxcustomerplatformbackend.facade.suborder.request.SubOrderPageRequest
import io.cliveyou.luxcustomerplatformbackend.facade.suborder.response.SubOrderResponse
import io.cliveyou.luxcustomerplatformbackend.facade.suborder.response.WaybillResponse
import io.cliveyou.luxcustomerplatformbackend.facade.tracking.response.TrackingInfoResponse
import io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa.SubOrderRepository
import io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa.WaybillRepository
import org.springframework.data.domain.Page
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class SubOrderApplicationService(
    private val waybillRepository: WaybillRepository,
    private val subOrderRepository: SubOrderRepository,
    private val trackingApplicationService: TrackingApplicationService,
) {

    /**
     * 分页查询子订单（原始实体）
     * 内部使用，返回完整的SubOrder实体
     */
    fun page(req: SubOrderPageRequest): Page<SubOrder> {
        return subOrderRepository.findAll(req.convertToSpecWithPermissions(), req.toPageable())
    }

    /**
     * 分页查询子订单（响应DTO）
     * 对外接口使用，只返回前端需要的字段
     */
    fun pageResponse(req: SubOrderPageRequest): Page<SubOrderResponse> {
        val subOrderPage = page(req)
        return subOrderPage.map { SubOrderResponse.from(it) }
    }

    /**
     * 获取子订单的轨迹信息
     *
     * @param subOrderId 子订单ID
     * @return 轨迹信息响应，如果未找到则返回null
     */
    fun getSubOrderTracking(subOrderId: Long): TrackingInfoResponse? {
        val subOrder = subOrderRepository.findById(subOrderId).orElse(null) ?: return null
        val orderNo = subOrder.orderNo ?: return null
        return trackingApplicationService.getTrackingByOrderNo(orderNo)
    }

    fun getWaybillInfo(id: Long): WaybillResponse? {
        val subOrder = subOrderRepository.findByIdOrNull(id) ?: throw CognitionException("suborder not exist")
        val waybill = waybillRepository.findByWaybillAccordingToOrderNo(subOrder.orderNo!!)
        return WaybillResponse(
            waybillNo = waybill.firstOrNull()?.waybillNo,
            pdfUrl = waybill.firstOrNull()?.shipping?.waybillLabelUrl,
            trackingNumber = waybill.firstOrNull()?.trackingNumber,
        )
    }
}