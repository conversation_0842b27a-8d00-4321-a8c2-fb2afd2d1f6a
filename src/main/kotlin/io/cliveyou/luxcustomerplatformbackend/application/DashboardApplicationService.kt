package io.cliveyou.luxcustomerplatformbackend.application

import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.convertToSpecWithPermissions
import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrder
import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrderStatus
import io.cliveyou.luxcustomerplatformbackend.facade.dashboard.request.DashboardStatsRequest
import io.cliveyou.luxcustomerplatformbackend.facade.dashboard.response.*
import io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa.SubOrderRepository
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import kotlin.math.max

@Service
class DashboardApplicationService(
    private val subOrderRepository: SubOrderRepository,
) {

    /**
     * 获取Dashboard概览统计
     */
    fun getOverviewStats(request: DashboardStatsRequest): DashboardOverviewResponse {
        val spec = request.convertToSpecWithPermissions()
        val allOrders = subOrderRepository.findAll(spec)
        
        // 计算各种统计数据
        val totalOrders = allOrders.size.toLong()
        val completedOrders = allOrders.count { it.status == SubOrderStatus.TRACKING_DELIVERED }.toLong()
        val processingOrders = allOrders.count { it.status.isProcessingStatus() }.toLong()
        val cancelledOrders = allOrders.count { it.status == SubOrderStatus.CANCELLED }.toLong()
        
        val completionRate = if (totalOrders > 0) {
            (completedOrders.toDouble() / totalOrders.toDouble()) * 100
        } else 0.0
        
        // 状态分布统计 - 使用显示状态进行分组
        val statusDistribution = allOrders.groupBy { it.status.getDisplayStatus() }
            .mapValues { it.value.size.toLong() }
        
        // 最近7天订单数
        val sevenDaysAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
        val recentOrdersCount = allOrders.count { it.createdAt >= sevenDaysAgo }.toLong()
        
        // 时间范围信息
        val dateRange = calculateDateRange(request.startDate, request.endDate)
        
        return DashboardOverviewResponse(
            totalOrders = totalOrders,
            completedOrders = completedOrders,
            processingOrders = processingOrders,
            cancelledOrders = cancelledOrders,
            completionRate = completionRate,
            statusDistribution = statusDistribution,
            recentOrdersCount = recentOrdersCount,
            dateRange = dateRange
        )
    }

    /**
     * 获取订单趋势数据
     */
    fun getOrderTrend(request: DashboardStatsRequest): DashboardOrderTrendResponse {
        val spec = request.convertToSpecWithPermissions()
        val allOrders = subOrderRepository.findAll(spec)
        
        // 按日期分组统计
        val dataPoints = generateTrendDataPoints(allOrders, request.startDate, request.endDate)
        
        // 计算汇总信息
        val summary = calculateTrendSummary(dataPoints, allOrders.size.toLong())
        
        return DashboardOrderTrendResponse(
            dataPoints = dataPoints,
            summary = summary
        )
    }

    /**
     * 生成趋势数据点
     */
    private fun generateTrendDataPoints(
        orders: List<SubOrder>,
        startDate: Long?,
        endDate: Long?
    ): List<OrderTrendDataPoint> {
        val now = System.currentTimeMillis()
        val start = startDate ?: (now - 30 * 24 * 60 * 60 * 1000L) // 默认30天前
        val end = endDate ?: now
        
        // 按日期分组
        val ordersByDate = orders.groupBy { order ->
            val date = Instant.ofEpochMilli(order.createdAt)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
            date
        }
        
        val dataPoints = mutableListOf<OrderTrendDataPoint>()
        val startLocalDate = Instant.ofEpochMilli(start).atZone(ZoneId.systemDefault()).toLocalDate()
        val endLocalDate = Instant.ofEpochMilli(end).atZone(ZoneId.systemDefault()).toLocalDate()
        
        var currentDate = startLocalDate
        while (!currentDate.isAfter(endLocalDate)) {
            val dayOrders = ordersByDate[currentDate] ?: emptyList()
            
            val dataPoint = OrderTrendDataPoint(
                date = currentDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
                timestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                orderCount = dayOrders.size.toLong(),
                completedCount = dayOrders.count { it.status == SubOrderStatus.TRACKING_DELIVERED }.toLong(),
                processingCount = dayOrders.count { it.status.isProcessingStatus() }.toLong(),
                cancelledCount = dayOrders.count { it.status == SubOrderStatus.CANCELLED }.toLong()
            )
            
            dataPoints.add(dataPoint)
            currentDate = currentDate.plusDays(1)
        }
        
        return dataPoints
    }

    /**
     * 计算趋势汇总信息
     */
    private fun calculateTrendSummary(
        dataPoints: List<OrderTrendDataPoint>,
        totalOrders: Long
    ): TrendSummary {
        val averageOrdersPerDay = if (dataPoints.isNotEmpty()) {
            totalOrders.toDouble() / dataPoints.size.toDouble()
        } else 0.0
        
        val peakDay = dataPoints.maxByOrNull { it.orderCount }
        
        // 简单的增长率计算（相比前一半时间段）
        val growthRate = calculateGrowthRate(dataPoints)
        
        return TrendSummary(
            totalOrders = totalOrders,
            averageOrdersPerDay = averageOrdersPerDay,
            peakDay = peakDay,
            growthRate = growthRate
        )
    }

    /**
     * 计算增长率
     */
    private fun calculateGrowthRate(dataPoints: List<OrderTrendDataPoint>): Double {
        if (dataPoints.size < 2) return 0.0
        
        val midPoint = dataPoints.size / 2
        val firstHalf = dataPoints.take(midPoint)
        val secondHalf = dataPoints.drop(midPoint)
        
        val firstHalfTotal = firstHalf.sumOf { it.orderCount }
        val secondHalfTotal = secondHalf.sumOf { it.orderCount }
        
        return if (firstHalfTotal > 0) {
            ((secondHalfTotal - firstHalfTotal).toDouble() / firstHalfTotal.toDouble()) * 100
        } else if (secondHalfTotal > 0) {
            100.0 // 从0增长到有订单
        } else {
            0.0
        }
    }

    /**
     * 计算时间范围信息
     */
    private fun calculateDateRange(startDate: Long?, endDate: Long?): DateRangeInfo {
        val now = System.currentTimeMillis()
        val start = startDate ?: (now - 30 * 24 * 60 * 60 * 1000L)
        val end = endDate ?: now
        
        val totalDays = max(1, ((end - start) / (24 * 60 * 60 * 1000L)).toInt())
        
        return DateRangeInfo(
            startDate = start,
            endDate = end,
            totalDays = totalDays
        )
    }
}
