package io.cliveyou.luxcustomerplatformbackend.application

import io.cliveyou.luxcustomerplatformbackend.common.exception.CognitionWebException
import io.cliveyou.luxcustomerplatformbackend.common.exception.OmsBaseErrorCode
import io.cliveyou.luxcustomerplatformbackend.common.utils.Jwt
import io.cliveyou.luxcustomerplatformbackend.facade.user.request.UserLoginRequest
import io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa.CustomerPlatformAccountRepository
import io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.redis.UserRedisRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

@Service
class UserLoginApplicationService(
    private val userRepository: CustomerPlatformAccountRepository,
    private val userRedisRepository: UserRedisRepository,
) {
    fun login(request: UserLoginRequest): String {
        val user = userRepository.findByEmailAndPassword(request.email, request.password)
            ?: throw CognitionWebException(OmsBaseErrorCode.INVALID_PASSWORD)
        val token = Jwt.createToken(user.id)
        userRedisRepository.saveUser(user, token)
        return token
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
