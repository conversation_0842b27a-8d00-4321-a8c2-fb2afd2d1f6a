package io.cliveyou.luxcustomerplatformbackend.domain.suborder

enum class SubOrderStatus {
    CREATED,
    CANCELLED,
    COMPLETED,
    SPLIT,
    SUPPLIER_MATCHED,
    FAILED,

    // 轨迹相关状态 - 与TrackingStatus对应
    TRACKING_NOT_FOUND,
    TRACKING_PRE_ADVICE_RECEIVED,
    TRACKING_PICKED_UP,
    TRACKING_IN_TRANSIT,
    TRACKING_ARRIVED_DESTINATION_COUNTRY,
    TRACKING_IN_CUSTOMS,
    TRACKING_CUSTOMS_CLEARED,
    TRACKING_ARRIVED_FOR_PICKUP,
    TRACKING_OUT_FOR_DELIVERY,
    TRACKING_DELIVERY_FAILED,
    TRACKING_DELIVERED,
    TRACKING_EXCEPTION,
    TRACKING_RETURNED,
    TRACKING_CANCELLED,
    TRACKING_UNKNOWN,
    ;

    fun onSplitStatus() = this == SPLIT

    /**
     * 获取用于页面显示的状态
     * SUPPLIER_MATCHED 和 FAILED 状态都映射为 PROCESSING（处理中）
     * 多个轨迹状态合并为简化的显示状态
     */
    fun getDisplayStatus(): String {
        return when (this) {
            SUPPLIER_MATCHED, FAILED -> "PROCESSING"

            // 轨迹状态合并
            TRACKING_NOT_FOUND, TRACKING_PRE_ADVICE_RECEIVED -> "TRACKING_PENDING"
            TRACKING_PICKED_UP -> "TRACKING_PICKED_UP"
            TRACKING_IN_TRANSIT, TRACKING_ARRIVED_DESTINATION_COUNTRY,
            TRACKING_IN_CUSTOMS, TRACKING_CUSTOMS_CLEARED,
                -> "TRACKING_IN_TRANSIT"

            TRACKING_ARRIVED_FOR_PICKUP, TRACKING_OUT_FOR_DELIVERY -> "TRACKING_OUT_FOR_DELIVERY"
            TRACKING_DELIVERED -> "TRACKING_DELIVERED"
            TRACKING_DELIVERY_FAILED, TRACKING_EXCEPTION,
            TRACKING_RETURNED, TRACKING_CANCELLED, TRACKING_UNKNOWN,
                -> "TRACKING_EXCEPTION"

            else -> this.name
        }
    }

    /**
     * 判断是否为处理中状态（SUPPLIER_MATCHED 或 FAILED）
     */
    fun isProcessingStatus() = this == SUPPLIER_MATCHED || this == FAILED ||
            this == TRACKING_NOT_FOUND
            || this == TRACKING_PRE_ADVICE_RECEIVED
            || this == TRACKING_PICKED_UP
            || this == TRACKING_IN_TRANSIT
            || this == TRACKING_ARRIVED_DESTINATION_COUNTRY
            || this == TRACKING_IN_CUSTOMS
            || this == TRACKING_CUSTOMS_CLEARED
            || this == TRACKING_ARRIVED_FOR_PICKUP
            || this == TRACKING_OUT_FOR_DELIVERY
            || this == TRACKING_DELIVERY_FAILED
            || this == TRACKING_EXCEPTION
            || this == TRACKING_RETURNED
            || this == TRACKING_CANCELLED
            || this == TRACKING_UNKNOWN


}
