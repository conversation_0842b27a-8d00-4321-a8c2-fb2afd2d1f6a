package io.cliveyou.luxcustomerplatformbackend.domain.suborder

import io.cliveyou.luxcustomerplatformbackend.common.enums.OrderSource
import io.cliveyou.luxcustomerplatformbackend.common.enums.WayBillStatus
import io.cliveyou.luxcustomerplatformbackend.config.jpa.AbstractBaseEntity
import io.cliveyou.luxcustomerplatformbackend.domain.vp.ProductInfo
import io.cliveyou.luxcustomerplatformbackend.domain.vp.RecipientInfo
import io.cliveyou.luxcustomerplatformbackend.domain.vp.ShippingInfo
import io.cliveyou.luxcustomerplatformbackend.nextId
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.*
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type

@Entity(name = "sub_orders")
class SubOrder : AbstractBaseEntity() {
    @Id
    @Column(name = "id", nullable = false)
    var id: Long = nextId()

    var parentId: Long = 0

    var originId: Long? = null

    var fileName: String? = null

    @Column(columnDefinition = "text", unique = true)
    var orderNo: String? = null

    var googleSearch: String? = null

    @Column(columnDefinition = "text")
    var urls: String? = null

    @Column(columnDefinition = "text")
    var effectUrl: String? = null

    @Column(columnDefinition = "text")
    var designUrl: String? = null

    @Embedded
    var recipient: RecipientInfo = RecipientInfo()

    @Embedded
    var product: ProductInfo = ProductInfo()

    @Embedded
    var shipping: ShippingInfo = ShippingInfo()

    @Column(name = "customer_id", nullable = false, columnDefinition = "bigint")
    var customerId: Long = 0

    @Column(name = "customer_name", nullable = false, columnDefinition = "text")
    var customerName: String = ""

    @Column(name = "biz_id", nullable = false, columnDefinition = "bigint")
    @ColumnDefault("1")
    var bizId: Long = 1

    @Enumerated(EnumType.STRING)
    var status = SubOrderStatus.CREATED

    @Column(nullable = true)
    @Enumerated(EnumType.STRING)
    var waybillStatus: WayBillStatus? = null

    @Type(ListArrayType::class)
    @Column(
        name = "order_download_task_ids",
        columnDefinition = "bigint[]",
    )
    var orderDownloadTaskIds: List<Long> = listOf()

    @ColumnDefault("'LUXOMS'")
    @Enumerated(EnumType.STRING)
    @Column(name = "source", nullable = false)
    var source: OrderSource = OrderSource.LUXOMS

    @ColumnDefault("''")
    @Column(name = "remark", nullable = false, columnDefinition = "text")
    var remark: String = ""

    @Column(name = "failed_message", nullable = true, columnDefinition = "text")
    var failedReason: String? = null

    @ColumnDefault("''")
    @Column(name = "customer_order_no", nullable = false, columnDefinition = "text")
    var customerOrderNo: String = ""


}
