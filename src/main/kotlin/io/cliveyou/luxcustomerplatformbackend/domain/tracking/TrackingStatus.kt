package io.cliveyou.luxcustomerplatformbackend.domain.tracking

/**
 * OMS系统统一的轨迹状态枚举
 * 将不同渠道的状态统一为平台标准状态
 */
enum class TrackingStatus(
    val code: String,
    val description: String,
    val displayName: String
) {
    /**
     * 未找到轨迹信息
     */
    NOT_FOUND("NOT_FOUND", "No tracking information found", "未找到"),

    /**
     * 电子预报信息已接收
     */
    PRE_ADVICE_RECEIVED("PRE_ADVICE_RECEIVED", "Electronic pre-advice received", "电子预报"),

    /**
     * 已收件/已揽收
     */
    PICKED_UP("PICKED_UP", "Package picked up", "已揽收"),

    /**
     * 运输途中
     */
    IN_TRANSIT("IN_TRANSIT", "In transit", "运输途中"),

    /**
     * 到达目的地国家
     */
    ARRIVED_DESTINATION_COUNTRY("ARRIVED_DESTINATION_COUNTRY", "Arrived at destination country", "到达目的地国家"),

    /**
     * 清关中
     */
    IN_CUSTOMS("IN_CUSTOMS", "In customs clearance", "清关中"),

    /**
     * 清关完成
     */
    CUSTOMS_CLEARED("CUSTOMS_CLEARED", "Customs cleared", "清关完成"),

    /**
     * 到达待取
     */
    ARRIVED_FOR_PICKUP("ARRIVED_FOR_PICKUP", "Arrived for pickup", "到达待取"),

    /**
     * 派送中
     */
    OUT_FOR_DELIVERY("OUT_FOR_DELIVERY", "Out for delivery", "派送中"),

    /**
     * 投递失败
     */
    DELIVERY_FAILED("DELIVERY_FAILED", "Delivery failed", "投递失败"),

    /**
     * 已签收/已投递
     */
    DELIVERED("DELIVERED", "Delivered", "已签收"),

    /**
     * 异常
     */
    EXCEPTION("EXCEPTION", "Exception occurred", "异常"),

    /**
     * 已退回
     */
    RETURNED("RETURNED", "Returned", "已退回"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "Cancelled", "已取消"),

    /**
     * 未知状态
     */
    UNKNOWN("UNKNOWN", "Unknown status", "未知状态");

    companion object {
        /**
         * 根据代码获取状态
         */
        fun fromCode(code: String): TrackingStatus = 
            entries.find { it.code == code } ?: UNKNOWN

        /**
         * 判断是否为最终状态（不会再变化的状态）
         */
        fun isFinalStatus(status: TrackingStatus): Boolean = 
            status in listOf(DELIVERED, RETURNED, CANCELLED)

        /**
         * 判断是否为异常状态
         */
        fun isExceptionStatus(status: TrackingStatus): Boolean = 
            status in listOf(DELIVERY_FAILED, EXCEPTION, RETURNED, CANCELLED)
    }
}
