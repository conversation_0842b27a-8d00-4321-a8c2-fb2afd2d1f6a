package io.cliveyou.luxcustomerplatformbackend.domain.tracking

import io.cliveyou.luxcustomerplatformbackend.config.jpa.AbstractBaseEntity
import io.cliveyou.luxcustomerplatformbackend.nextId
import io.cliveyou.luxcustomerplatformbackend.domain.tracking.TrackingStatus
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.Type
import java.time.ZonedDateTime

/**
 * OMS系统统一的轨迹信息实体
 * 存储包裹的完整轨迹信息
 */
@Entity
@Table(
    name = "tracking_info",
    indexes = [
        // 核心索引
        Index(name = "idx_tracking_waybill_id", columnList = "waybill_id"),
        Index(name = "idx_tracking_channel", columnList = "channel"),
        Index(name = "idx_tracking_current_status", columnList = "current_status"),
        Index(name = "idx_tracking_last_updated_at", columnList = "last_updated_at"),

        // 复合索引 - 高频查询优化
        Index(name = "idx_tracking_update_query", columnList = "last_updated_at, last_event_time"),
        Index(name = "idx_tracking_stats_query", columnList = "created_at, current_status, channel"),
        Index(name = "idx_tracking_page_query", columnList = "last_updated_at, id"),
    ]
)
data class TrackingInfo(
    @Id
    val id: Long = nextId(),

    @Type(ListArrayType::class)
    @Column(
        name = "order_nos",
        columnDefinition = "text[]",
    )
    val orderNos: List<String>,

    @Column(name = "waybill_id", nullable = false)
    val waybillId: Long,

    /**
     * 主订单ID
     */
    @Column(name = "main_order_id", nullable = false)
    val mainOrderId: Long,

    /**
     * 运单号
     */
    @Column(name = "waybill_no", nullable = false, unique = true)
    val waybillNo: String,
    /**
     * 跟踪号
     */
    @Column(name = "tracking_number")
    var trackingNumber: String? = null,
    /**
     * 物流渠道
     */
    @Column(name = "channel", nullable = false)
    val channel: String,
    /**
     * 当前状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "current_status", nullable = false)
    var currentStatus: TrackingStatus = TrackingStatus.NOT_FOUND,
    /**
     * 目的地国家代码
     */
    @Column(name = "destination_country")
    var destinationCountry: String? = null,
    /**
     * 起始国家代码
     */
    @Column(name = "origin_country")
    var originCountry: String? = null,
    /**
     * 末端服务商信息
     */
    @Type(value = JsonType::class)
    @Column(name = "last_mile_provider", columnDefinition = "jsonb")
    var lastMileProvider: LastMileProvider? = null,
    /**
     * 轨迹事件列表
     */
    @Type(value = JsonType::class)
    @Column(name = "tracking_events", columnDefinition = "jsonb")
    var trackingEvents: List<TrackingEvent> = emptyList(),
    /**
     * 签收天数
     */
    @Column(name = "delivery_days")
    var deliveryDays: Int? = null,
    /**
     * 妥投证明链接
     */
    @Type(value = JsonType::class)
    @Column(name = "pod_links", columnDefinition = "jsonb")
    var podLinks: List<String> = emptyList(),
    /**
     * 最后更新时间（定时任务执行时间）
     */
    @Column(name = "last_updated_at")
    var lastUpdatedAt: ZonedDateTime = ZonedDateTime.now(),

    /**
     * 最后事件时间（轨迹的最新事件发生时间）
     */
    @Column(name = "last_event_time")
    var lastEventTime: ZonedDateTime? = null,

    /**
     * 原始响应数据（用于调试和数据恢复）
     */
    @Type(value = JsonType::class)
    @Column(name = "raw_data", columnDefinition = "jsonb")
    var rawData: Map<String, Any> = emptyMap(),
) : AbstractBaseEntity() {

    /**
     * 获取最新的轨迹事件
     */
    fun getLatestEvent(): TrackingEvent? = trackingEvents.maxByOrNull { it.eventTime }

    /**
     * 获取指定状态的事件
     */
    fun getEventsByStatus(status: TrackingStatus): List<TrackingEvent> = trackingEvents.filter { it.status == status }

    /**
     * 判断是否有异常
     */
    fun hasException(): Boolean = TrackingStatus.Companion.isExceptionStatus(currentStatus)

}

/**
 * 末端服务商信息
 */
data class LastMileProvider(
    /**
     * 服务商名称
     */
    val name: String,
    /**
     * 联系电话
     */
    val telephone: String? = null,
    /**
     * 官网地址
     */
    val website: String? = null,
)
