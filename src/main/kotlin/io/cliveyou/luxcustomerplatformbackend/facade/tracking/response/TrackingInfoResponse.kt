package io.cliveyou.luxcustomerplatformbackend.facade.tracking.response

import io.cliveyou.luxcustomerplatformbackend.domain.tracking.TrackingInfo
import io.cliveyou.luxcustomerplatformbackend.domain.tracking.TrackingStatus
import io.cliveyou.luxcustomerplatformbackend.domain.tracking.TrackingEvent
import io.cliveyou.luxcustomerplatformbackend.domain.tracking.LocationDetail
import io.cliveyou.luxcustomerplatformbackend.domain.tracking.LastMileProvider

/**
 * 轨迹信息响应DTO
 * 只包含前端需要的字段
 */
data class TrackingInfoResponse(
    val id: Long,
    val waybillNo: String,
    val trackingNumber: String?,
    val channel: String,
    val currentStatus: TrackingStatus,
    val destinationCountry: String?,
    val originCountry: String?,
    val lastMileProvider: LastMileProviderResponse?,
    val trackingEvents: List<TrackingEventResponse>,
    val deliveryDays: Int?,
    val podLinks: List<String>,
    val lastUpdatedAt: Long, // 时间戳（毫秒）
    val lastEventTime: Long?, // 时间戳（毫秒）
) {
    companion object {
        fun from(trackingInfo: TrackingInfo): TrackingInfoResponse {
            return TrackingInfoResponse(
                id = trackingInfo.id,
                waybillNo = trackingInfo.waybillNo,
                trackingNumber = trackingInfo.trackingNumber,
                channel = trackingInfo.channel,
                currentStatus = trackingInfo.currentStatus,
                destinationCountry = trackingInfo.destinationCountry,
                originCountry = trackingInfo.originCountry,
                lastMileProvider = trackingInfo.lastMileProvider?.let { LastMileProviderResponse.from(it) },
                trackingEvents = trackingInfo.trackingEvents.map { TrackingEventResponse.from(it) },
                deliveryDays = trackingInfo.deliveryDays,
                podLinks = trackingInfo.podLinks,
                lastUpdatedAt = trackingInfo.lastUpdatedAt.toInstant().toEpochMilli(),
                lastEventTime = trackingInfo.lastEventTime?.toInstant()?.toEpochMilli(),
            )
        }
    }
}

/**
 * 轨迹事件响应DTO
 */
data class TrackingEventResponse(
    val eventTime: Long, // 时间戳（毫秒）
    val status: TrackingStatus,
    val description: String,
    val location: String?,
    val locationDetail: LocationDetailResponse?,
    val isLastMileEvent: Boolean,
    val extraInfo: Map<String, String>,
    val originalStatusCode: String?,
    val originalStatusDescription: String?,
) {
    companion object {
        fun from(trackingEvent: TrackingEvent): TrackingEventResponse {
            return TrackingEventResponse(
                eventTime = trackingEvent.eventTime.toInstant().toEpochMilli(),
                status = trackingEvent.status,
                description = trackingEvent.description,
                location = trackingEvent.location,
                locationDetail = trackingEvent.locationDetail?.let { LocationDetailResponse.from(it) },
                isLastMileEvent = trackingEvent.isLastMileEvent,
                extraInfo = trackingEvent.extraInfo,
                originalStatusCode = trackingEvent.originalStatusCode,
                originalStatusDescription = trackingEvent.originalStatusDescription,
            )
        }
    }
}

/**
 * 地址详细信息响应DTO
 */
data class LocationDetailResponse(
    val country: String?,
    val province: String?,
    val city: String?,
    val postCode: String?,
    val address: String?,
) {
    companion object {
        fun from(locationDetail: LocationDetail): LocationDetailResponse {
            return LocationDetailResponse(
                country = locationDetail.country,
                province = locationDetail.province,
                city = locationDetail.city,
                postCode = locationDetail.postCode,
                address = locationDetail.address,
            )
        }
    }
}

/**
 * 末端服务商信息响应DTO
 */
data class LastMileProviderResponse(
    val name: String,
    val telephone: String?,
    val website: String?,
) {
    companion object {
        fun from(lastMileProvider: LastMileProvider): LastMileProviderResponse {
            return LastMileProviderResponse(
                name = lastMileProvider.name,
                telephone = lastMileProvider.telephone,
                website = lastMileProvider.website,
            )
        }
    }
}
