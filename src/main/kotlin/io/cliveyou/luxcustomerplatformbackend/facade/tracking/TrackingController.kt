package io.cliveyou.luxcustomerplatformbackend.facade.tracking

import io.cliveyou.luxcustomerplatformbackend.application.TrackingApplicationService
import io.cliveyou.luxcustomerplatformbackend.facade.tracking.response.TrackingInfoResponse
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 轨迹信息控制器
 * 提供轨迹查询相关接口
 */
@RestController
@RequestMapping("/tracking")
class TrackingController(
    private val trackingApplicationService: TrackingApplicationService,
) {

    /**
     * 根据订单号查询轨迹信息
     * 
     * @param orderNo 订单号
     * @return 轨迹信息响应
     */
    @GetMapping("/order/{orderNo}")
    fun getTrackingByOrderNo(@PathVariable orderNo: String): TrackingInfoResponse? {
        return trackingApplicationService.getTrackingByOrderNo(orderNo)
    }
}
