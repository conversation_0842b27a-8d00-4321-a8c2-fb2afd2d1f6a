package io.cliveyou.luxcustomerplatformbackend.facade.dashboard

import io.cliveyou.luxcustomerplatformbackend.application.DashboardApplicationService
import io.cliveyou.luxcustomerplatformbackend.facade.dashboard.request.DashboardStatsRequest
import io.cliveyou.luxcustomerplatformbackend.facade.dashboard.response.DashboardOrderTrendResponse
import io.cliveyou.luxcustomerplatformbackend.facade.dashboard.response.DashboardOverviewResponse
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/dashboard")
class DashboardController(
    private val dashboardApplicationService: DashboardApplicationService,
) {

    /**
     * 获取Dashboard概览统计
     * 
     * 权限控制：用户只能查看自己的订单统计
     */
    @PostMapping("/overview")
    fun getOverview(@RequestBody request: DashboardStatsRequest): DashboardOverviewResponse {
        return dashboardApplicationService.getOverviewStats(request)
    }

    /**
     * 获取订单趋势数据
     * 
     * 权限控制：用户只能查看自己的订单趋势
     */
    @PostMapping("/order-trend")
    fun getOrderTrend(@RequestBody request: DashboardStatsRequest): DashboardOrderTrendResponse {
        return dashboardApplicationService.getOrderTrend(request)
    }
}
