package io.cliveyou.luxcustomerplatformbackend.facade.dashboard.request

import io.cliveyou.luxcustomerplatformbackend.common.web.request.SpecRequest
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.RequireUserPermission
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.SpecBuilder
import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrder
import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrderStatus
import org.springframework.data.jpa.domain.Specification

/**
 * Dashboard统计查询请求
 *
 * 权限控制：用户只能查询自己的订单统计
 */
@RequireUserPermission
data class DashboardStatsRequest(
    val startDate: Long? = null,  // 开始时间戳（毫秒）
    val endDate: Long? = null,    // 结束时间戳（毫秒）
) : SpecRequest<SubOrder> {

    override fun convertToSpec(): Specification<SubOrder> {
        return SpecBuilder.of<SubOrder>()
            .notInList(SubOrder::status, listOf(SubOrderStatus.SPLIT))
            .between(SubOrder::createdAt, startDate, endDate)
            .build()
    }
}
