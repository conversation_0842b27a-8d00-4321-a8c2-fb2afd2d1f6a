package io.cliveyou.luxcustomerplatformbackend.facade.dashboard.response

import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrderStatus

/**
 * Dashboard概览统计响应
 */
data class DashboardOverviewResponse(
    val totalOrders: Long,                    // 总订单数
    val completedOrders: Long,                // 已完成订单数
    val processingOrders: Long,               // 处理中订单数
    val cancelledOrders: Long,                // 已取消订单数
    val completionRate: Double,               // 完成率（百分比）
    val statusDistribution: Map<String, Long>, // 状态分布（使用显示状态）
    val recentOrdersCount: Long,              // 最近7天订单数
    val dateRange: DateRangeInfo              // 查询时间范围信息
)

/**
 * 时间范围信息
 */
data class DateRangeInfo(
    val startDate: Long?,                     // 开始时间戳
    val endDate: Long?,                       // 结束时间戳
    val totalDays: Int                        // 总天数
)

/**
 * 订单趋势数据点
 */
data class OrderTrendDataPoint(
    val date: String,                         // 日期（YYYY-MM-DD格式）
    val timestamp: Long,                      // 时间戳
    val orderCount: Long,                     // 当日订单数
    val completedCount: Long,                 // 当日完成订单数
    val processingCount: Long,                // 当日处理中订单数
    val cancelledCount: Long                  // 当日取消订单数
)

/**
 * 订单趋势响应
 */
data class DashboardOrderTrendResponse(
    val dataPoints: List<OrderTrendDataPoint>, // 趋势数据点
    val summary: TrendSummary                  // 趋势汇总信息
)

/**
 * 趋势汇总信息
 */
data class TrendSummary(
    val totalOrders: Long,                    // 总订单数
    val averageOrdersPerDay: Double,          // 日均订单数
    val peakDay: OrderTrendDataPoint?,        // 订单最多的一天
    val growthRate: Double                    // 增长率（相比上一周期）
)
