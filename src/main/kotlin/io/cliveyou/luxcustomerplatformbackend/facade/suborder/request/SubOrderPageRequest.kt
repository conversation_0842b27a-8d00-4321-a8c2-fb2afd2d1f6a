package io.cliveyou.luxcustomerplatformbackend.facade.suborder.request

import io.cliveyou.luxcustomerplatformbackend.common.web.request.OmsPageRequest
import io.cliveyou.luxcustomerplatformbackend.common.web.request.SpecRequest
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.RequireUserPermission
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.SpecBuilder
import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrder
import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrderStatus
import org.springframework.data.jpa.domain.Specification

/**
 * 子订单分页查询请求
 *
 * 权限控制：用户只能查询自己的订单（customerId 权限控制）
 */
@RequireUserPermission  // 自动添加 customerId 权限控制
data class SubOrderPageRequest(
    val end: Long? = null,
    val start: Long? = null,
    val orderNo: String? = null,
    val customerOrderNo: String? = null,
    val status: String? = null, // 状态筛选，支持逗号分隔的多个状态
) : SpecRequest<SubOrder>, OmsPageRequest() {

    override fun convertToSpec(): Specification<SubOrder> {
        val statusList = parseStatusList()

        return SpecBuilder.of<SubOrder>()
            .like(SubOrder::orderNo, orderNo)
            .like(SubOrder::customerOrderNo, customerOrderNo)
            .inList(SubOrder::status, statusList)
            .between(SubOrder::createdAt, start, end)
            .build()
    }

    /**
     * 解析状态筛选参数
     * 支持 'PROCESSING' 特殊值，会被转换为 ['SUPPLIER_MATCHED', 'FAILED']
     * 默认排除 SPLIT 状态
     */
    private fun parseStatusList(): List<SubOrderStatus>? {
        if (status.isNullOrBlank()) {
            // 如果没有指定状态筛选，返回所有状态除了 SPLIT
            return SubOrderStatus.values().filter { it != SubOrderStatus.SPLIT }
        }

        val statusList = status.split(",").map { it.trim() }.filter { it.isNotBlank() }
        if (statusList.isEmpty()) {
            // 如果状态列表为空，返回所有状态除了 SPLIT
            return SubOrderStatus.values().filter { it != SubOrderStatus.SPLIT }
        }

        val result = mutableListOf<SubOrderStatus>()

        statusList.forEach { statusStr ->
            when (statusStr) {
                "PROCESSING" -> {
                    // 'PROCESSING' 转换为实际的两个状态
                    result.add(SubOrderStatus.SUPPLIER_MATCHED)
                    result.add(SubOrderStatus.FAILED)
                }
                "TRACKING_PENDING" -> {
                    // 'TRACKING_PENDING' 转换为实际的状态
                    result.add(SubOrderStatus.TRACKING_NOT_FOUND)
                    result.add(SubOrderStatus.TRACKING_PRE_ADVICE_RECEIVED)
                }
                "TRACKING_IN_TRANSIT" -> {
                    // 'TRACKING_IN_TRANSIT' 转换为实际的状态
                    result.add(SubOrderStatus.TRACKING_IN_TRANSIT)
                    result.add(SubOrderStatus.TRACKING_ARRIVED_DESTINATION_COUNTRY)
                    result.add(SubOrderStatus.TRACKING_IN_CUSTOMS)
                    result.add(SubOrderStatus.TRACKING_CUSTOMS_CLEARED)
                }
                "TRACKING_OUT_FOR_DELIVERY" -> {
                    // 'TRACKING_OUT_FOR_DELIVERY' 转换为实际的状态
                    result.add(SubOrderStatus.TRACKING_ARRIVED_FOR_PICKUP)
                    result.add(SubOrderStatus.TRACKING_OUT_FOR_DELIVERY)
                }
                "TRACKING_EXCEPTION" -> {
                    // 'TRACKING_EXCEPTION' 转换为实际的状态
                    result.add(SubOrderStatus.TRACKING_DELIVERY_FAILED)
                    result.add(SubOrderStatus.TRACKING_EXCEPTION)
                    result.add(SubOrderStatus.TRACKING_RETURNED)
                    result.add(SubOrderStatus.TRACKING_CANCELLED)
                    result.add(SubOrderStatus.TRACKING_UNKNOWN)
                }
                else -> {
                    try {
                        val statusEnum = SubOrderStatus.valueOf(statusStr)
                        // 排除 SPLIT 状态
                        if (statusEnum != SubOrderStatus.SPLIT) {
                            result.add(statusEnum)
                        }
                    } catch (e: IllegalArgumentException) {
                        // 忽略无效的状态值
                    }
                }
            }
        }

        return if (result.isEmpty()) null else result.distinct()
    }
}
