package io.cliveyou.luxcustomerplatformbackend.facade.suborder

import io.cliveyou.luxcustomerplatformbackend.application.SubOrderApplicationService
import io.cliveyou.luxcustomerplatformbackend.application.TrackingApplicationService
import io.cliveyou.luxcustomerplatformbackend.facade.suborder.request.SubOrderPageRequest
import io.cliveyou.luxcustomerplatformbackend.facade.suborder.response.SubOrderResponse
import io.cliveyou.luxcustomerplatformbackend.facade.suborder.response.WaybillResponse
import io.cliveyou.luxcustomerplatformbackend.facade.tracking.response.TrackingInfoResponse
import org.springframework.data.domain.Page
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/sub-orders")
class SubOrderController(
    private val subOrderApplicationService: SubOrderApplicationService,
    private val trackingApplicationService: TrackingApplicationService,
) {

    /**
     * 分页查询子订单
     *
     * 权限控制：用户只能查询自己的订单
     * 响应优化：只返回前端需要的字段，避免暴露敏感信息
     */
    @PostMapping("/search")
    fun search(@RequestBody request: SubOrderPageRequest): Page<SubOrderResponse> {
        return subOrderApplicationService.pageResponse(request)
    }

    /**
     * 获取子订单的轨迹信息
     *
     * @param id 子订单ID
     * @return 轨迹信息响应，如果未找到则返回null
     */
    @GetMapping("/{id}/tracking")
    fun getSubOrderTracking(@PathVariable id: Long): TrackingInfoResponse? {
        return subOrderApplicationService.getSubOrderTracking(id)
    }


    @GetMapping("/{id}/waybill")
    fun fetchWaybillInfo(@PathVariable id: Long): WaybillResponse? {
        return subOrderApplicationService.getWaybillInfo(id)
    }

}
