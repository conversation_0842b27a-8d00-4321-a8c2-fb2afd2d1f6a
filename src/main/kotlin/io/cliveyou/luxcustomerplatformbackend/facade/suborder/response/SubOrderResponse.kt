package io.cliveyou.luxcustomerplatformbackend.facade.suborder.response

import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrder
import io.cliveyou.luxcustomerplatformbackend.domain.suborder.SubOrderStatus

/**
 * 子订单分页查询响应DTO
 * 只包含前端表格和详情卡片需要的字段，避免暴露敏感信息
 */
data class SubOrderResponse(
    val id: Long,
    val customerOrderNo: String,
    val status: SubOrderStatus,
    val displayStatus: String, // 用于前端显示的状态，SUPPLIER_MATCHED 和 FAILED 都显示为 PROCESSING
    val recipient: RecipientInfoResponse,
    val product: ProductInfoResponse,
    val shipping: ShippingInfoResponse,
    val createdAt: Long,
) {
    companion object {
        fun from(subOrder: SubOrder): SubOrderResponse {
            return SubOrderResponse(
                id = subOrder.id,
                customerOrderNo = subOrder.customerOrderNo,
                status = subOrder.status,
                displayStatus = subOrder.status.getDisplayStatus(),
                recipient = RecipientInfoResponse.from(subOrder.recipient),
                product = ProductInfoResponse.from(subOrder.product),
                shipping = ShippingInfoResponse.from(subOrder.shipping),
                createdAt = subOrder.createdAt,
            )
        }
    }
}

/**
 * 收件人信息响应DTO
 * 只包含前端显示需要的字段
 */
data class RecipientInfoResponse(
    val userName: String?,
    val receiverName: String?,
    val country: String?,
    val state: String?,
    val city: String?,
    val address1: String?,
    val address2: String?,
    val postcode: String?,
    val phone: String?,
    val email: String?,
) {
    companion object {
        fun from(recipient: io.cliveyou.luxcustomerplatformbackend.domain.vp.RecipientInfo): RecipientInfoResponse {
            return RecipientInfoResponse(
                userName = recipient.userName,
                receiverName = recipient.receiverName,
                country = recipient.country,
                state = recipient.state,
                city = recipient.city,
                address1 = recipient.address1,
                address2 = recipient.address2,
                postcode = recipient.postcode,
                phone = recipient.phone,
                email = recipient.email,
            )
        }
    }
}

/**
 * 商品信息响应DTO
 * 只包含前端显示需要的字段
 */
data class ProductInfoResponse(
    val spu: String?,
    val size: String?,
    val color: String?,
    val qty: Int,
    val name: String,
    val title: String,
) {
    companion object {
        fun from(product: io.cliveyou.luxcustomerplatformbackend.domain.vp.ProductInfo): ProductInfoResponse {
            return ProductInfoResponse(
                spu = product.spu,
                size = product.size,
                color = product.color,
                qty = product.qty,
                name = product.name,
                title = product.title,
            )
        }
    }
}

/**
 * 物流信息响应DTO
 * 保留完整结构以备将来使用，目前前端只显示占位符
 */
data class ShippingInfoResponse(
    val channel: String,
    val shipMethod: String?,
    val wayBillRelation: String?,
    val deliveryMethod: String?,
    val waybillLabelUrl: String?,
) {
    companion object {
        fun from(shipping: io.cliveyou.luxcustomerplatformbackend.domain.vp.ShippingInfo): ShippingInfoResponse {
            return ShippingInfoResponse(
                channel = shipping.channel.name,
                shipMethod = shipping.shipMethod,
                wayBillRelation = shipping.wayBillRelation,
                deliveryMethod = shipping.deliveryMethod?.name,
                waybillLabelUrl = shipping.waybillLabelUrl,
            )
        }
    }
}
