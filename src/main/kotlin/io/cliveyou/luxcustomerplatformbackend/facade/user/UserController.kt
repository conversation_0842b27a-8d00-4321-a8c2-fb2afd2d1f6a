package io.cliveyou.luxcustomerplatformbackend.facade.user

import io.cliveyou.luxcustomerplatformbackend.application.UserLoginApplicationService
import io.cliveyou.luxcustomerplatformbackend.config.auth.UserContext
import io.cliveyou.luxcustomerplatformbackend.facade.user.request.UserLoginRequest
import io.cliveyou.luxcustomerplatformbackend.config.auth.UserContextHolder
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.servlet.http.Cookie
import jakarta.servlet.http.HttpServletResponse
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/users")
class UserController(private val userLoginApplicationService: UserLoginApplicationService) {
    @PostMapping("/login")
    fun login(
        @RequestBody user: UserLoginRequest,
        response: HttpServletResponse,
    ): String {
        log.info { "login request: $user" }
        val token = userLoginApplicationService.login(user)

        // 设置token到cookie
        val cookie =
            <PERSON>ie("authToken", token).apply {
                isHttpOnly = true // 防止XSS攻击
                secure = false // 如果是HTTPS环境，设置为true
                path = "/"
                maxAge = 7 * 24 * 60 * 60 // 7天过期
            }
        response.addCookie(cookie)

        return "登录成功"
    }

    @PostMapping("/logout")
    fun logout(response: HttpServletResponse): String {
        // 清除cookie
        val cookie =
            Cookie("authToken", "").apply {
                isHttpOnly = true
                secure = false
                path = "/"
                maxAge = 0 // 立即过期
            }
        response.addCookie(cookie)
        return "退出成功"
    }

    @GetMapping("/profile")
    fun profile(): UserContext? = UserContextHolder.user

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
