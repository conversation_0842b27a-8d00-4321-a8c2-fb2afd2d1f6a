package io.cliveyou.luxcustomerplatformbackend.common.enums

enum class WayBillStatus {
    CREATED,
    PENDING,
    COMPLETED,
    FAILED,
    CANCELLED,
    ;

    companion object {
        fun fromString(value: String): WayBillStatus =
            when (value) {
                "CREATED" -> CREATED
                "PENDING" -> PENDING
                "COMPLETED" -> COMPLETED
                "FAILED" -> FAILED
                "CANCELLED" -> CANCELLED
                else -> throw IllegalArgumentException()
            }
    }

}