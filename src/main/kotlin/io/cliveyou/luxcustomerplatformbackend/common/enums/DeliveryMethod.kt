package io.cliveyou.luxcustomerplatformbackend.common.enums

import com.fasterxml.jackson.annotation.JsonValue

enum class DeliveryMethod(
    private val display: String,
) {
    SUPPLY_DELIVERY("工厂发货"),

    COMPANY_DELIVERY("公司发货"),
    ;

    @JsonValue
    fun getDisplay(): String = display

    companion object {
        fun fromString(value: String): DeliveryMethod =
            when (value) {
                "SUPPLY_DELIVERY" -> SUPPLY_DELIVERY
                "COMPANY_DELIVERY" -> COMPANY_DELIVERY
                else -> throw kotlin.IllegalArgumentException()
            }
    }
}
