package io.cliveyou.luxcustomerplatformbackend.common.web.request

import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort

/** 通用分页请求DTO */
open class OmsPageRequest(
    open val page: Int = 0,
    open val size: Int = 10,
    open val sortBy: String? = null,
    open val sortDirection: String? = null,
) {
    /** 转换为Spring Data的Pageable对象 */
    fun toPageable(): Pageable {
        return if (sortBy.isNullOrBlank()) {
            PageRequest.of(page, size,Sort.by(Sort.Direction.DESC, "createdAt"))
        } else {
            val direction =
                if (sortDirection?.equals("desc", ignoreCase = true) == true) {
                    Sort.Direction.DESC
                } else {
                    Sort.Direction.ASC
                }
            PageRequest.of(page, size, Sort.by(direction, sortBy))
        }
    }

    /** 支持多字段排序的版本 */
    fun toPageableWithMultiSort(sortFields: Map<String, Sort.Direction>?): Pageable {
        return if (sortFields.isNullOrEmpty()) {
            toPageable()
        } else {
            val orders = sortFields.map { Sort.Order(it.value, it.key) }
            PageRequest.of(page, size, Sort.by(orders))
        }
    }
}
