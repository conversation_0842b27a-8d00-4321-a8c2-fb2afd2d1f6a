package io.cliveyou.luxcustomerplatformbackend.common.exception

import org.springframework.http.HttpStatus

open class CognitionWebException(
    private val baseErrorCodeEnum: BaseErrorCodeEnum,
    vararg args: Any?,
) : CognitionException(baseErrorCodeEnum.msg.format(*args.map { it?.toString() ?: "" }.toTypedArray())) {
    fun code(): String = baseErrorCodeEnum.code

    fun httpCode(): HttpStatus = baseErrorCodeEnum.httpCode

    fun msg(): String = message
}
