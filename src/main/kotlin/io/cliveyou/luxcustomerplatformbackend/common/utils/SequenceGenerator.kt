package io.cliveyou.luxcustomerplatformbackend.common.utils

import java.net.NetworkInterface
import java.security.SecureRandom
import java.time.Instant
import kotlin.math.pow

/**
 * Distributed Sequence Generator.
 * Inspired by Twitter snowflake: https://github.com/twitter/snowflake/tree/snowflake-2010
 * This class should be used as a Singleton.
 * Make sure that you create and reuse a Single instance of SequenceGenerator per node in your distributed system cluster.
 */
class SequenceGenerator {
    private val nodeId: Int

    private var lastTimestamp = -1L
    private var sequence = 0L

    // Create SequenceGenerator with a nodeId
    constructor(nodeId: Int) {
        require(nodeId in 0..maxNodeId) { "NodeId must be between 0 and $maxNodeId" }
        this.nodeId = nodeId
    }

    // Let SequenceGenerator generate a nodeId
    constructor() {
        this.nodeId = createNodeId()
    }

    @Synchronized
    fun nextId(): Long {
        var currentTimestamp = timestamp()
        check(currentTimestamp >= lastTimestamp) { "Invalid System Clock!" }

        if (currentTimestamp == lastTimestamp) {
            sequence = (sequence + 1) and maxSequence.toLong()
            if (sequence == 0L) {
                // Sequence Exhausted, wait till next millisecond.
                currentTimestamp = waitNextMillis(currentTimestamp)
            }
        } else {
            // reset sequence to start with zero for the next millisecond
            sequence = 0
        }

        lastTimestamp = currentTimestamp

        var id = currentTimestamp shl (NODE_ID_BITS + SEQUENCE_BITS)
        id = id or (nodeId.toLong() shl SEQUENCE_BITS)
        id = id or sequence
        return id
    }

    // Get current timestamp in milliseconds, adjust for the custom epoch.
    private fun timestamp(): Long = Instant.now().toEpochMilli() - CUSTOM_EPOCH

    // Block and wait till next millisecond
    private fun waitNextMillis(currentTimestamp: Long): Long {
        var timestamp = currentTimestamp
        while (timestamp == lastTimestamp) {
            timestamp = timestamp()
        }
        return timestamp
    }

    private fun createNodeId(): Int {
        val sb = StringBuilder()
        try {
            val networkInterfaces = NetworkInterface.getNetworkInterfaces()
            while (networkInterfaces.hasMoreElements()) {
                val networkInterface = networkInterfaces.nextElement()
                val mac = networkInterface.hardwareAddress
                if (mac != null) {
                    for (i in mac.indices) {
                        sb.append(String.format("%02X", mac[i]))
                    }
                }
            }
        } catch (ex: Exception) {
            return SecureRandom().nextInt() and maxNodeId
        }
        return sb.toString().hashCode() and maxNodeId
    }

    companion object {
        private const val UNUSED_BITS = 1 // Sign bit, Unused (always set to 0)
        private const val EPOCH_BITS = 41
        private const val NODE_ID_BITS = 10
        private const val SEQUENCE_BITS = 12

        private val maxNodeId = (2.0.pow(NODE_ID_BITS.toDouble()) - 1).toInt()
        private val maxSequence = (2.0.pow(SEQUENCE_BITS.toDouble()) - 1).toInt()

        private const val CUSTOM_EPOCH = 1704038400000L
    }
}
