package io.cliveyou.luxcustomerplatformbackend

import io.cliveyou.luxcustomerplatformbackend.common.utils.SequenceGenerator
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.data.web.config.EnableSpringDataWebSupport
import org.springframework.data.web.config.EnableSpringDataWebSupport.PageSerializationMode

@SpringBootApplication
@EnableSpringDataWebSupport(pageSerializationMode = PageSerializationMode.VIA_DTO)
class LuxCustomerPlatformBackendApplication

fun main(args: Array<String>) {
    runApplication<LuxCustomerPlatformBackendApplication>(*args)
}

object SequenceGeneratorUtils {
    val sequence = SequenceGenerator()
}

fun nextId(): Long = SequenceGeneratorUtils.sequence.nextId()
