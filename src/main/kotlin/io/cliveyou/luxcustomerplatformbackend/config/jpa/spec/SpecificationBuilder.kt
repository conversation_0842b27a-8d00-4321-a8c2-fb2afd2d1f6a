package io.cliveyou.jpasetup.domain.spec

import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.and
import io.cliveyou.luxcustomerplatformbackend.config.jpa.spec.or
import org.springframework.data.jpa.domain.Specification
import kotlin.reflect.KProperty1

class SpecificationBuilder<T> {
    private val specs = mutableListOf<Pair<String, Specification<T>>>()

    fun and(spec: Specification<T>) {
        specs.add("and" to spec)
    }

    fun or(spec: Specification<T>) {
        specs.add("or" to spec)
    }

    /**
     * 不等于查询
     */
    fun <V> notEqual(property: KProperty1<T, V>, value: V?): SpecificationBuilder<T> {
        if (value != null) {
            val spec = Specification<T> { root, _, cb ->
                cb.notEqual(root.get<V>(property.name), value)
            }
            specs.add("and" to spec)
        }
        return this
    }

    /**
     * 不在列表中查询
     */
    fun <V> notInList(property: KProperty1<T, V>, values: List<V>?): SpecificationBuilder<T> {
        if (!values.isNullOrEmpty()) {
            val spec = Specification<T> { root, _, cb ->
                cb.not(root.get<V>(property.name).`in`(values))
            }
            specs.add("and" to spec)
        }
        return this
    }

    /**
     * 通用否定方法 - 对任何 Specification 进行否定
     */
    fun not(spec: Specification<T>): SpecificationBuilder<T> {
        val notSpec = Specification { root, query, cb ->
            cb.not(spec.toPredicate(root, query, cb))
        }
        specs.add("and" to notSpec)
        return this
    }

    fun build(): Specification<T>? {
        if (specs.isEmpty()) return null
        var result: Specification<T> = specs.first().second
        for (i in 1 until specs.size) {
            val (logic, spec) = specs[i]
            result = if (logic == "and") result and spec else result or spec
        }
        return result
    }
}

fun <T> buildSpecification(block: SpecificationBuilder<T>.() -> Unit): Specification<T> {
    val builder = SpecificationBuilder<T>()
    builder.block()
    return builder.build() ?: throw IllegalArgumentException("No specification provided")
}

/**
 * 创建简单的不等于查询
 */
fun <T, V> notEqual(property: KProperty1<T, V>, value: V?): Specification<T>? {
    return if (value != null) {
        Specification { root, _, cb ->
            cb.notEqual(root.get<V>(property.name), value)
        }
    } else null
}

/**
 * 创建简单的不在列表中查询
 */
fun <T, V> notInList(property: KProperty1<T, V>, values: List<V>?): Specification<T>? {
    return if (!values.isNullOrEmpty()) {
        Specification { root, _, cb ->
            cb.not(root.get<V>(property.name).`in`(values))
        }
    } else null
}

/**
 * 创建否定的 Specification
 */
fun <T> not(spec: Specification<T>): Specification<T> {
    return Specification { root, query, cb ->
        cb.not(spec.toPredicate(root, query, cb))
    }
}