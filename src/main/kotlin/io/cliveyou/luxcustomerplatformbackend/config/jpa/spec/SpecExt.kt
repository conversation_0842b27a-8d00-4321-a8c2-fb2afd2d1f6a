package io.cliveyou.luxcustomerplatformbackend.config.jpa.spec

import io.cliveyou.luxcustomerplatformbackend.config.auth.UserContextHolder
import org.springframework.data.jpa.domain.Specification
import kotlin.reflect.KProperty1

/**
 * JPA Specification 构建器，提供类型安全的查询条件构建
 *
 * 使用方式：
 * ```kotlin
 * val spec = SpecBuilder.of<User>()
 *     .equal(User::name, "张三")
 *     .greaterThan(User::age, 18)
 *     .like(User::email, "gmail")
 *     .build()
 * ```
 */
class SpecBuilder<T> private constructor() {

    private val specifications = mutableListOf<Specification<T>>()

    companion object {
        /**
         * 创建指定实体类型的 Specification 构建器
         */
        fun <T> of(): SpecBuilder<T> = SpecBuilder()

        /**
         * 创建带权限控制的 Specification 构建器
         * 自动注入当前用户的 customerId 权限控制
         */
        fun <T> withUserPermission(customerIdProperty: KProperty1<T, Long>): SpecBuilder<T> {
            val builder = SpecBuilder<T>()
            val currentUser = UserContextHolder.user
            if (currentUser?.customerId != null) {
                builder.equal(customerIdProperty, currentUser.customerId)
            }
            return builder
        }

        /**
         * 创建带权限控制的 Specification 构建器（支持可空 customerId）
         * 自动注入当前用户的 customerId 权限控制
         */
        fun <T> withUserPermissionNullable(customerIdProperty: KProperty1<T, Long?>): SpecBuilder<T> {
            val builder = SpecBuilder<T>()
            val currentUser = UserContextHolder.user
            if (currentUser?.customerId != null) {
                builder.equal(customerIdProperty, currentUser.customerId)
            }
            return builder
        }

        /**
         * 创建空的 Specification（匹配所有记录）
         */
        fun <T> empty(): Specification<T> = Specification { _, _, cb -> cb.conjunction() }
    }

    /**
     * 等值查询
     */
    fun < V> equal(property: KProperty1<T, V>, value: V?): SpecBuilder<T> {
        if (value != null) {
            specifications.add(Specification { root, _, cb ->
                cb.equal(root.get<V>(property.name), value)
            })
        }
        return this
    }

    /**
     * 大于查询
     */
      fun < V : Comparable<V>> greaterThan(property: KProperty1<T, V>, value: V?): SpecBuilder<T> {
        if (value != null) {
            specifications.add(Specification { root, _, cb ->
                cb.greaterThan(root.get<V>(property.name), value)
            })
        }
        return this
    }

    /**
     * 小于查询
     */
    private  fun < V : Comparable<V>> lessThan(property: KProperty1<T, V?>, value: V?): SpecBuilder<T> {
        if (value != null) {
            specifications.add(Specification { root, _, cb ->
                cb.lessThan(root.get<V>(property.name), value)
            })
        }
        return this
    }

    /**
     * 模糊查询（包含）- 支持可空和非空字符串
     */
    fun like(property: KProperty1<T, String?>, pattern: String?): SpecBuilder<T> {
        if (!pattern.isNullOrBlank()) {
            specifications.add(Specification { root, _, cb ->
                cb.like(root.get(property.name), "%$pattern%")
            })
        }
        return this
    }

    /**
     * 左模糊查询（以...结尾）- 支持可空和非空字符串
     */
    fun leftLike(property: KProperty1<T, String?>, pattern: String?): SpecBuilder<T> {
        if (!pattern.isNullOrBlank()) {
            specifications.add(Specification { root, _, cb ->
                cb.like(root.get(property.name), "%$pattern")
            })
        }
        return this
    }

    /**
     * 右模糊查询（以...开头）- 支持可空和非空字符串
     */
    fun rightLike(property: KProperty1<T, String?>, pattern: String?): SpecBuilder<T> {
        if (!pattern.isNullOrBlank()) {
            specifications.add(Specification { root, _, cb ->
                cb.like(root.get(property.name), "$pattern%")
            })
        }
        return this
    }

    /**
     * 范围查询
     */
    fun <V : Comparable<V>> between(
        property: KProperty1<T, V>,
        start: V?,
        end: V?,
    ): SpecBuilder<T> {
        if (start != null && end != null) {
            specifications.add(Specification { root, _, cb ->
                cb.between(root.get<V>(property.name), start, end)
            })
        }
        return this
    }

    /**
     * IN 查询
     */
    fun <V> inList(property: KProperty1<T, V>, values: List<V>?): SpecBuilder<T> {
        if (!values.isNullOrEmpty()) {
            specifications.add(Specification { root, _, cb ->
                root.get<V>(property.name).`in`(values)
            })
        }
        return this
    }

    /**
     * IS NULL 查询
     */
    fun <V> isNull(property: KProperty1<T, V>): SpecBuilder<T> {
        specifications.add(Specification { root, _, cb ->
            cb.isNull(root.get<V>(property.name))
        })
        return this
    }

    /**
     * IS NOT NULL 查询
     */
    fun <V> isNotNull(property: KProperty1<T, V>): SpecBuilder<T> {
        specifications.add(Specification { root, _, cb ->
            cb.isNotNull(root.get<V>(property.name))
        })
        return this
    }

    /**
     * 不等于查询
     */
    fun <V> notEqual(property: KProperty1<T, V>, value: V?): SpecBuilder<T> {
        if (value != null) {
            specifications.add(Specification { root, _, cb ->
                cb.notEqual(root.get<V>(property.name), value)
            })
        }
        return this
    }

    /**
     * 不在列表中查询
     */
    fun <V> notInList(property: KProperty1<T, V>, values: List<V>?): SpecBuilder<T> {
        if (!values.isNullOrEmpty()) {
            specifications.add(Specification { root, _, cb ->
                cb.not(root.get<V>(property.name).`in`(values))
            })
        }
        return this
    }

    /**
     * 通用否定方法 - 对任何 Specification 进行否定
     */
    fun not(spec: Specification<T>): SpecBuilder<T> {
        specifications.add(Specification { root, query, cb ->
            cb.not(spec.toPredicate(root, query, cb))
        })
        return this
    }

    /**
     * 添加自定义 Specification
     */
    fun custom(spec: Specification<T>): SpecBuilder<T> {
        specifications.add(spec)
        return this
    }

    /**
     * 添加当前用户权限控制（customerId）
     */
    fun withCurrentUserPermission(customerIdProperty: KProperty1<T, Long>): SpecBuilder<T> {
        val currentUser = UserContextHolder.user
        if (currentUser?.customerId != null) {
            equal(customerIdProperty, currentUser.customerId)
        }
        return this
    }

    /**
     * 添加当前用户权限控制（支持可空 customerId）
     */
    fun withCurrentUserPermissionNullable(customerIdProperty: KProperty1<T, Long?>): SpecBuilder<T> {
        val currentUser = UserContextHolder.user
        if (currentUser?.customerId != null) {
            equal(customerIdProperty, currentUser.customerId)
        }
        return this
    }

    /**
     * 添加业务租户权限控制（bizId）
     */
    fun withBizPermission(bizIdProperty: KProperty1<T, Long>): SpecBuilder<T> {
        val currentUser = UserContextHolder.user
        if (currentUser?.bizId != null && currentUser.bizId > 0) {
            equal(bizIdProperty, currentUser.bizId)
        }
        return this
    }

    /**
     * 构建最终的 Specification（AND 连接）
     */
    fun build(): Specification<T> {
        return when {
            specifications.isEmpty() -> empty()
            specifications.size == 1 -> specifications.first()
            else -> specifications.reduce { acc, spec -> acc.and(spec) }
        }
    }

    /**
     * 构建最终的 Specification（OR 连接）
     */
    fun buildOr(): Specification<T> {
        return when {
            specifications.isEmpty() -> empty()
            specifications.size == 1 -> specifications.first()
            else -> specifications.reduce { acc, spec -> acc.or(spec) }
        }
    }
}

/**
 * Specification 扩展函数，提供更简洁的组合方式
 */
infix fun <T> Specification<T>.and(other: Specification<T>): Specification<T> {
    return this.and(other)
}

infix fun <T> Specification<T>.or(other: Specification<T>): Specification<T> {
    return this.or(other)
}
