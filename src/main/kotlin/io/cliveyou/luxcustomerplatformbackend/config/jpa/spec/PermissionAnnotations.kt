package io.cliveyou.luxcustomerplatformbackend.config.jpa.spec

/**
 * 标记需要用户权限控制的请求类
 * 
 * @param customerIdField 实体中表示客户ID的字段名
 * @param nullable 客户ID字段是否可空
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class RequireUserPermission(
    val customerIdField: String = "customerId",
    val nullable: Boolean = false
)

/**
 * 标记需要租户权限控制的请求类
 * 
 * @param bizIdField 实体中表示租户ID的字段名
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class RequireBizPermission(
    val bizIdField: String = "bizId"
)

/**
 * 标记管理员可以绕过权限控制的请求类
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class AdminBypass

/**
 * 权限控制类型枚举
 */
enum class PermissionType {
    USER,      // 用户级权限（customerId）
    BIZ,       // 租户级权限（bizId）
    ADMIN,     // 管理员权限
    PUBLIC     // 公共数据（无权限控制）
}

/**
 * 复合权限控制注解
 * 
 * @param types 权限控制类型列表
 * @param logic 多个权限之间的逻辑关系（AND/OR）
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class RequirePermissions(
    val types: Array<PermissionType>,
    val logic: PermissionLogic = PermissionLogic.AND
)

/**
 * 权限逻辑关系
 */
enum class PermissionLogic {
    AND,  // 所有权限都必须满足
    OR    // 满足任一权限即可
}
