package io.cliveyou.luxcustomerplatformbackend.config.jpa.spec

import io.cliveyou.luxcustomerplatformbackend.common.web.request.SpecRequest
import io.cliveyou.luxcustomerplatformbackend.config.auth.UserContextHolder
import org.springframework.data.jpa.domain.Specification
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.memberProperties

/**
 * 权限 Specification 处理器
 * 根据注解自动添加权限控制条件
 */
@Suppress("UNCHECKED_CAST")
object PermissionSpecProcessor {

    /**
     * 为 SpecRequest 自动添加权限控制
     *
     * @param request 请求对象
     * @param entityClass 实体类
     * @param baseSpec 基础查询条件
     * @return 添加权限控制后的 Specification
     */
    fun <T : Any, R : SpecRequest<T>> processPermissions(
        request: R,
        entityClass: KClass<T>,
        baseSpec: Specification<T>,
    ): Specification<T> {
        val requestClass = request::class

        // 检查用户权限注解
        val userPermission = requestClass.findAnnotation<RequireUserPermission>()
        if (userPermission != null) {
            val customerIdProperty = findProperty(entityClass, userPermission.customerIdField)
            if (customerIdProperty != null) {
                val permissionSpec = createUserPermissionSpec<T>(customerIdProperty, userPermission.nullable)
                return baseSpec.and(permissionSpec)
            }
        }

        // 检查租户权限注解
        val bizPermission = requestClass.findAnnotation<RequireBizPermission>()
        if (bizPermission != null) {
            val bizIdProperty = findProperty(entityClass, bizPermission.bizIdField)
            if (bizIdProperty != null) {
                val permissionSpec = createBizPermissionSpec<T>(bizIdProperty)
                return baseSpec.and(permissionSpec)
            }
        }

        // 检查复合权限注解
        val permissions = requestClass.findAnnotation<RequirePermissions>()
        if (permissions != null) {
            return processComplexPermissions(entityClass, baseSpec, permissions)
        }

        return baseSpec
    }

    /**
     * 处理复合权限控制
     */
    private fun <T : Any> processComplexPermissions(
        entityClass: KClass<T>,
        baseSpec: Specification<T>,
        permissions: RequirePermissions,
    ): Specification<T> {
        val permissionSpecs = mutableListOf<Specification<T>>()

        for (type in permissions.types) {
            when (type) {
                PermissionType.USER -> {
                    val customerIdProperty = findProperty(entityClass, "customerId")
                    if (customerIdProperty != null) {
                        permissionSpecs.add(createUserPermissionSpec<T>(customerIdProperty, false))
                    }
                }

                PermissionType.BIZ -> {
                    val bizIdProperty = findProperty(entityClass, "bizId")
                    if (bizIdProperty != null) {
                        permissionSpecs.add(createBizPermissionSpec<T>(bizIdProperty))
                    }
                }

                PermissionType.PUBLIC -> {
                    // 公共数据不需要权限控制
                    continue
                }

                PermissionType.ADMIN -> {
                    // 管理员权限检查
                    val currentUser = UserContextHolder.user
                    if (isAdmin()) {
                        return baseSpec // 管理员跳过权限控制
                    }
                }
            }
        }

        if (permissionSpecs.isEmpty()) {
            return baseSpec
        }

        val combinedPermissionSpec = when (permissions.logic) {
            PermissionLogic.AND -> permissionSpecs.reduce { acc, spec -> acc.and(spec) }
            PermissionLogic.OR -> permissionSpecs.reduce { acc, spec -> acc.or(spec) }
        }

        return baseSpec.and(combinedPermissionSpec)
    }

    /**
     * 创建用户权限 Specification
     */
    private fun <T : Any> createUserPermissionSpec(
        customerIdProperty: KProperty1<T, *>,
        nullable: Boolean,
    ): Specification<T> {
        val currentUser = UserContextHolder.user
        return if (currentUser?.customerId != null) {
            if (nullable) {
                SpecBuilder.of<T>().equal(customerIdProperty as KProperty1<T, Long?>, currentUser.customerId).build()
            } else {
                SpecBuilder.of<T>().equal(customerIdProperty as KProperty1<T, Long>, currentUser.customerId).build()
            }
        } else {
            // 如果没有用户上下文，返回永远不匹配的条件
            Specification { _, _, cb -> cb.disjunction() }
        }
    }

    /**
     * 创建租户权限 Specification
     */
    private fun <T : Any> createBizPermissionSpec(bizIdProperty: KProperty1<T, *>): Specification<T> {
        val currentUser = UserContextHolder.user
        return if (currentUser?.bizId != null && currentUser.bizId > 0) {
            SpecBuilder.of<T>().equal(bizIdProperty as KProperty1<T, Long>, currentUser.bizId).build()
        } else {
            // 如果没有租户信息，返回永远不匹配的条件
            Specification { _, _, cb -> cb.disjunction() }
        }
    }

    /**
     * 查找实体类中的属性
     */
    private fun <T : Any> findProperty(entityClass: KClass<T>, propertyName: String): KProperty1<T, *>? {
        return entityClass.memberProperties.find { it.name == propertyName }
    }
}

/**
 * UserContext 扩展函数，检查是否为管理员
 */
private fun isAdmin(): Boolean {
    // 这里可以根据实际的管理员判断逻辑来实现
    // 例如：检查角色、权限等
    return false // 暂时返回 false，需要根据实际业务逻辑实现
}

/**
 * SpecRequest 扩展函数，自动处理权限控制
 */
inline fun <reified T : Any, R : SpecRequest<T>> R.convertToSpecWithPermissions(): Specification<T> {
    val baseSpec = this.convertToSpec()
    return PermissionSpecProcessor.processPermissions(this, T::class, baseSpec)
}
