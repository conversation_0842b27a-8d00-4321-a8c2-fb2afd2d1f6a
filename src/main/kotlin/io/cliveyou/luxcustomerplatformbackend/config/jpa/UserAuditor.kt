package io.cliveyou.luxcustomerplatformbackend.config.jpa

import io.cliveyou.luxcustomerplatformbackend.config.auth.UserContextHolder
import org.springframework.context.annotation.Configuration
import org.springframework.data.domain.AuditorAware
import org.springframework.data.jpa.repository.config.EnableJpaAuditing
import java.util.*

@Configuration
@EnableJpaAuditing
class UserAuditor : AuditorAware<Long> {
    override fun getCurrentAuditor(): Optional<Long> {
        if (UserContextHolder.user == null) {
            return Optional.empty()
        }
        val id = UserContextHolder.user!!.id
        return Optional.ofNullable(id)
    }
}
