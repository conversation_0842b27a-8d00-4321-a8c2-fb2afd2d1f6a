package io.cliveyou.luxcustomerplatformbackend.config.web

import io.cliveyou.luxcustomerplatformbackend.config.auth.ApiSignatureInterceptor
import org.springframework.stereotype.Component
import org.springframework.web.servlet.config.annotation.CorsRegistry
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Component
class WebConfig(
    private val apiSignatureInterceptor: ApiSignatureInterceptor,
) : WebMvcConfigurer {
    override fun addInterceptors(registry: InterceptorRegistry) {
        registry.addInterceptor(apiSignatureInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                        "/google/callback",
                        "/test/**",
                        "/api/openapi/**",
                        "/error",
                )
    }

    override fun addCorsMappings(registry: CorsRegistry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*") // 使用allowedOriginPatterns支持cookie
                .allowedMethods("*")
                .allowedHeaders("*")
                .allowCredentials(true) // 允许跨域发送cookie
    }
}
