package io.cliveyou.luxcustomerplatformbackend.config.auth

import io.cliveyou.luxcustomerplatformbackend.common.utils.Jwt
import io.cliveyou.luxcustomerplatformbackend.config.auth.UserContext.Companion.fromUser
import io.cliveyou.luxcustomerplatformbackend.domain.CustomerPlatformAccount
import io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa.CustomerPlatformAccountRepository
import io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.redis.UserRedisRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import org.springframework.web.servlet.ModelAndView

data class UserContext(
    val id: Long,
    val token: String,
    val name: String = "",
    val account: String = "",
    val createdBy: Long? = null,
    val updatedBy: Long? = null,
    var bizId: Long = -1,
    var customerId: Long? = null,
) {
    companion object {
        fun fromUser(
            user: CustomerPlatformAccount,
            newToken: String,
        ): UserContext =
            UserContext(
                id = user.id,
                token = newToken,
                name = user.accountName,
                account= user.email,
                bizId = user.bizId,
                customerId = user.customerId,
            )
    }
}

@Component
class ApiSignatureInterceptor(
    private val userRedisRepository: UserRedisRepository,
    private val userRepository: CustomerPlatformAccountRepository,
) : HandlerInterceptor {
    @Throws(Exception::class)
    override fun preHandle(
        httpServletRequest: HttpServletRequest,
        httpServletResponse: HttpServletResponse,
        `object`: Any,
    ): Boolean {
        if (httpServletRequest.method == "OPTIONS") {
            return true
        }

        // 跳过登录和登出接口的鉴权
        val requestURI = httpServletRequest.requestURI
        if (requestURI.endsWith("/login") || requestURI.endsWith("/logout")) {
            return true
        }

        // 从cookie中获取token
        val cookies = httpServletRequest.cookies
        val authToken = cookies?.find { it.name == "authToken" }?.value

        if (authToken.isNullOrBlank()) {
            httpServletResponse.sendError(401, "Unauthorized")
            return false
        }

        val userId =
            try {
                Jwt.extractUserFromJwt(authToken)["userId"]!!.asLong()
            } catch (e: Exception) {
                log.error(e) { "JWT error:" }
                httpServletResponse.sendError(401, "Unauthorized")
                return false
            }
        val check = userRedisRepository.findUserIdByToken(userId, authToken)

        if (check == null) {
            httpServletResponse.sendError(401, "Unauthorized")
            return false
        }
        val user = userRepository.findByIdOrNull(userId)
        if (user == null) {
            httpServletResponse.sendError(401, "Unauthorized")
            return false
        }

        UserContextHolder.set(fromUser(user, authToken))
        return true
    }

    override fun postHandle(
        request: HttpServletRequest,
        response: HttpServletResponse,
        o: Any,
        modelAndView: ModelAndView?,
    ) {
        UserContextHolder.remove()
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}

fun HttpServletRequest.clientIp(): String? {
    val xfHeader = getHeader("X-Forwarded-For") ?: return remoteAddr
    return xfHeader.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()[
        0] // for proxies
}
