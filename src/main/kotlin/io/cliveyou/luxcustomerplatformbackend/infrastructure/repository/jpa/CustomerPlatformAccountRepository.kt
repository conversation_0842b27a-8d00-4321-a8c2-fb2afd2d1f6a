package io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa

import io.cliveyou.luxcustomerplatformbackend.domain.CustomerPlatformAccount
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CustomerPlatformAccountRepository : JpaRepository<CustomerPlatformAccount, Long> {

    fun findByEmailAndPassword(account: String, password: String): CustomerPlatformAccount?

}
