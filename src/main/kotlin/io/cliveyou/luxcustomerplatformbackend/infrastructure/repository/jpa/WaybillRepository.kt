@file:Suppress("JpaQlInspection", "SqlNoDataSourceInspection")

package io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa

import io.cliveyou.luxcustomerplatformbackend.domain.waybill.Waybill
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface WaybillRepository : JpaRepository<Waybill, Long> {
    fun findByOrderNo(orderNo: String): Waybill?

    @Query(
        """
        SELECT w.* FROM waybills w
        WHERE string_to_array(w.order_nos, ',') @> ARRAY[cast(:orderNo AS text)]::text[]
        """,
        nativeQuery = true
    )
    fun findByWaybillAccordingToOrderNo(orderNo: String): List<Waybill>

}
