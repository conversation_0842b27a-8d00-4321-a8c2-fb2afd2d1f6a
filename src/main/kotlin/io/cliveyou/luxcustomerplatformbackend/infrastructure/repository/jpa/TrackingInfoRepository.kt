package io.cliveyou.luxcustomerplatformbackend.infrastructure.repository.jpa

import io.cliveyou.luxcustomerplatformbackend.domain.tracking.TrackingInfo
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface TrackingInfoRepository : JpaRepository<TrackingInfo, Long> {

    /**
     * 根据订单号查找轨迹信息
     * 使用PostgreSQL的数组查询语法，需要使用原生SQL
     */
    @Query(
        value = "SELECT * FROM tracking_info WHERE order_nos @> ARRAY[cast(:orderNo AS text)]::text[]",
        nativeQuery = true
    )
    fun findByOrderNo(@Param("orderNo") orderNo: String): TrackingInfo?
}