import type { SubOrderStatus } from "@/api/sub-order/sub-order-model";

// 定义简化的显示状态类型
export type DisplayStatus =
    | 'PROCESSING'
    | 'TRACKING_PENDING'
    | 'TRACKING_PICKED_UP'
    | 'TRACKING_IN_TRANSIT'
    | 'TRACKING_OUT_FOR_DELIVERY'
    | 'TRACKING_DELIVERED'
    | 'TRACKING_EXCEPTION';

export interface StatusOption {
    label: string;
    value: SubOrderStatus | DisplayStatus; // 支持原始状态和显示状态
}

export const getSubOrderStatusOptions = (t: (key: string) => string): StatusOption[] => [
    { label: t('subOrder.status.CREATED'), value: 'CREATED' },
    { label: t('subOrder.status.CANCELLED'), value: 'CANCELLED' },
    { label: t('subOrder.status.PROCESSING'), value: 'PROCESSING' }, // 合并的"处理中"选项
    // 简化后的轨迹相关状态
    { label: t('subOrder.status.TRACKING_PENDING'), value: 'TRACKING_PENDING' },
    { label: t('subOrder.status.TRACKING_PICKED_UP'), value: 'TRACKING_PICKED_UP' },
    { label: t('subOrder.status.TRACKING_IN_TRANSIT'), value: 'TRACKING_IN_TRANSIT' },
    { label: t('subOrder.status.TRACKING_OUT_FOR_DELIVERY'), value: 'TRACKING_OUT_FOR_DELIVERY' },
    { label: t('subOrder.status.TRACKING_DELIVERED'), value: 'TRACKING_DELIVERED' },
    { label: t('subOrder.status.TRACKING_EXCEPTION'), value: 'TRACKING_EXCEPTION' },
];

/**
 * 获取用于开发调试的完整状态选项（包含原始的 SUPPLIER_MATCHED 和 FAILED）
 */
export const getSubOrderStatusOptionsDebug = (t: (key: string) => string): StatusOption[] => [
    { label: t('subOrder.status.CREATED'), value: 'CREATED' },
    { label: t('subOrder.status.COMPLETED'), value: 'COMPLETED' },
    { label: t('subOrder.status.CANCELLED'), value: 'CANCELLED' },
    { label: t('subOrder.status.SPLIT'), value: 'SPLIT' },
    { label: t('subOrder.status.SUPPLIER_MATCHED'), value: 'SUPPLIER_MATCHED' },
    { label: t('subOrder.status.FAILED'), value: 'FAILED' },
    { label: t('subOrder.status.PROCESSING'), value: 'PROCESSING' }, // 合并选项
    // 简化后的轨迹相关状态
    { label: t('subOrder.status.TRACKING_PENDING'), value: 'TRACKING_PENDING' },
    { label: t('subOrder.status.TRACKING_PICKED_UP'), value: 'TRACKING_PICKED_UP' },
    { label: t('subOrder.status.TRACKING_IN_TRANSIT'), value: 'TRACKING_IN_TRANSIT' },
    { label: t('subOrder.status.TRACKING_OUT_FOR_DELIVERY'), value: 'TRACKING_OUT_FOR_DELIVERY' },
    { label: t('subOrder.status.TRACKING_DELIVERED'), value: 'TRACKING_DELIVERED' },
    { label: t('subOrder.status.TRACKING_EXCEPTION'), value: 'TRACKING_EXCEPTION' },
];

/**
 * 获取状态的显示标签
 * 将多个原始状态合并为简化的显示状态
 */
export const getSubOrderStatusLabel = (status: SubOrderStatus, t: (key: string) => string): string => {
    const displayStatus = getDisplayStatusFromOriginal(status);
    return t(`subOrder.status.${displayStatus}`);
};

/**
 * 将原始状态转换为显示状态
 * 与后端的 getDisplayStatus() 方法保持一致
 */
export const getDisplayStatusFromOriginal = (status: SubOrderStatus): string => {
    switch (status) {
        case 'SUPPLIER_MATCHED':
        case 'FAILED':
            return 'PROCESSING';

        // 轨迹状态合并
        case 'TRACKING_NOT_FOUND':
        case 'TRACKING_PRE_ADVICE_RECEIVED':
            return 'TRACKING_PENDING';

        case 'TRACKING_PICKED_UP':
            return 'TRACKING_PICKED_UP';

        case 'TRACKING_IN_TRANSIT':
        case 'TRACKING_ARRIVED_DESTINATION_COUNTRY':
        case 'TRACKING_IN_CUSTOMS':
        case 'TRACKING_CUSTOMS_CLEARED':
            return 'TRACKING_IN_TRANSIT';

        case 'TRACKING_ARRIVED_FOR_PICKUP':
        case 'TRACKING_OUT_FOR_DELIVERY':
            return 'TRACKING_OUT_FOR_DELIVERY';

        case 'TRACKING_DELIVERED':
            return 'TRACKING_DELIVERED';

        case 'TRACKING_DELIVERY_FAILED':
        case 'TRACKING_EXCEPTION':
        case 'TRACKING_RETURNED':
        case 'TRACKING_CANCELLED':
        case 'TRACKING_UNKNOWN':
            return 'TRACKING_EXCEPTION';

        default:
            return status;
    }
};

/**
 * 将筛选器中的状态值转换为实际的查询状态
 * 将简化的显示状态转换为对应的原始状态列表
 */
export const convertFilterStatusToQueryStatus = (filterStatuses: string[]): string[] => {
    const queryStatuses: string[] = [];

    filterStatuses.forEach(status => {
        switch (status) {
            case 'PROCESSING':
                queryStatuses.push('SUPPLIER_MATCHED', 'FAILED');
                break;
            case 'TRACKING_PENDING':
                queryStatuses.push('TRACKING_NOT_FOUND', 'TRACKING_PRE_ADVICE_RECEIVED');
                break;
            case 'TRACKING_IN_TRANSIT':
                queryStatuses.push(
                    'TRACKING_IN_TRANSIT',
                    'TRACKING_ARRIVED_DESTINATION_COUNTRY',
                    'TRACKING_IN_CUSTOMS',
                    'TRACKING_CUSTOMS_CLEARED'
                );
                break;
            case 'TRACKING_OUT_FOR_DELIVERY':
                queryStatuses.push('TRACKING_ARRIVED_FOR_PICKUP', 'TRACKING_OUT_FOR_DELIVERY');
                break;
            case 'TRACKING_EXCEPTION':
                queryStatuses.push(
                    'TRACKING_DELIVERY_FAILED',
                    'TRACKING_EXCEPTION',
                    'TRACKING_RETURNED',
                    'TRACKING_CANCELLED',
                    'TRACKING_UNKNOWN'
                );
                break;
            default:
                queryStatuses.push(status);
                break;
        }
    });

    // 去重
    return [...new Set(queryStatuses)];
};

/**
 * 检查状态是否匹配筛选条件
 * 支持简化状态筛选匹配对应的原始状态
 */
export const isStatusMatchFilter = (status: SubOrderStatus, filterStatuses: string[]): boolean => {
    if (filterStatuses.length === 0) return true;

    // 获取当前状态的显示状态
    const displayStatus = getDisplayStatusFromOriginal(status);

    // 检查显示状态是否在筛选条件中
    if (filterStatuses.includes(displayStatus)) {
        return true;
    }

    // 直接匹配原始状态
    return filterStatuses.includes(status);
};