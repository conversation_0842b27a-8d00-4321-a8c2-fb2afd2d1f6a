import { apiClient } from "@/lib/apiClient";
import type { OrganizationDailyBillResponse } from "./dash-model";
import type {
    DashboardStatsRequest,
    DashboardOverviewResponse,
    DashboardOrderTrendResponse
} from "../dashboard/dashboard-model";

export const dashStatisticApi = {

    getDashStatistic: async (params: {
        selectedYear: number;
        selectedMonth: number;
        organizationId: string;
    }) => {
        return await apiClient.get<Record<string, OrganizationDailyBillResponse[]>>({
            url: '/api/private/organizationDailyBill/charts',
            params
        })
    }
}

export const dashboardApi = {
    /**
     * 获取Dashboard概览统计
     */
    getOverview: async (request: DashboardStatsRequest): Promise<DashboardOverviewResponse> => {
        return await apiClient.post<DashboardOverviewResponse>({
            url: '/api/dashboard/overview',
            data: request
        });
    },

    /**
     * 获取订单趋势数据
     */
    getOrderTrend: async (request: DashboardStatsRequest): Promise<DashboardOrderTrendResponse> => {
        // 添加调试日志
        console.log('Dashboard API getOrderTrend - request:', request);

        return await apiClient.post<DashboardOrderTrendResponse>({
            url: '/api/dashboard/order-trend',
            data: request
        });
    }
}