import { apiClient } from "@/lib/apiClient";
import type { TrackingInfoResponse } from "./tracking-model";

export const trackingApi = {
  /**
   * 根据子订单ID获取轨迹信息
   * @param subOrderId 子订单ID
   * @returns 轨迹信息响应
   */
  getSubOrderTracking: async (subOrderId: number): Promise<TrackingInfoResponse | null> => {
    try {
      return await apiClient.get<TrackingInfoResponse>({
        url: `/api/sub-orders/${subOrderId}/tracking`
      });
    } catch (error) {
      // 如果返回404或其他错误，返回null表示没有轨迹信息
      return null;
    }
  },

  /**
   * 根据订单号获取轨迹信息
   * @param orderNo 订单号
   * @returns 轨迹信息响应
   */
  getTrackingByOrderNo: async (orderNo: string): Promise<TrackingInfoResponse | null> => {
    try {
      return await apiClient.get<TrackingInfoResponse>({
        url: `/api/tracking/order/${orderNo}`
      });
    } catch (error) {
      // 如果返回404或其他错误，返回null表示没有轨迹信息
      return null;
    }
  }
};
