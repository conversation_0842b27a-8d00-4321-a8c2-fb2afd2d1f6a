// 轨迹相关的 TypeScript 类型定义

export type TrackingStatus =
  | 'NOT_FOUND'
  | 'PRE_ADVICE_RECEIVED'
  | 'PICKED_UP'
  | 'IN_TRANSIT'
  | 'ARRIVED_DESTINATION_COUNTRY'
  | 'IN_CUSTOMS'
  | 'CUSTOMS_CLEARED'
  | 'ARRIVED_FOR_PICKUP'
  | 'OUT_FOR_DELIVERY'
  | 'DELIVERY_FAILED'
  | 'DELIVERED'
  | 'EXCEPTION'
  | 'RETURNED'
  | 'CANCELLED'
  | 'UNKNOWN';

export interface TrackingInfoResponse {
  id: number;
  waybillNo: string;
  trackingNumber?: string;
  channel: string;
  currentStatus: TrackingStatus;
  destinationCountry?: string;
  originCountry?: string;
  lastMileProvider?: LastMileProviderResponse;
  trackingEvents: TrackingEventResponse[];
  deliveryDays?: number;
  podLinks: string[];
  lastUpdatedAt: string; // 时间戳（毫秒）
  lastEventTime?: string; // 时间戳（毫秒）
}

export interface TrackingEventResponse {
  eventTime: string; // 时间戳（毫秒）
  status: TrackingStatus;
  description: string;
  location?: string;
  locationDetail?: LocationDetailResponse;
  isLastMileEvent: boolean;
  extraInfo: Record<string, string>;
  originalStatusCode?: string;
  originalStatusDescription?: string;
}

export interface LocationDetailResponse {
  country?: string;
  province?: string;
  city?: string;
  postCode?: string;
  address?: string;
}

export interface LastMileProviderResponse {
  name: string;
  telephone?: string;
  website?: string;
}
