// Dashboard相关的TypeScript类型定义

export interface DashboardStatsRequest {
  startDate?: number;  // 开始时间戳（毫秒）
  endDate?: number;    // 结束时间戳（毫秒）
}

export interface DashboardOverviewResponse {
  totalOrders: number;                    // 总订单数
  completedOrders: number;                // 已完成订单数
  processingOrders: number;               // 处理中订单数
  cancelledOrders: number;                // 已取消订单数
  completionRate: number;                 // 完成率（百分比）
  statusDistribution: Record<string, number>; // 状态分布
  recentOrdersCount: number;              // 最近7天订单数
  dateRange: DateRangeInfo;               // 查询时间范围信息
}

export interface DateRangeInfo {
  startDate?: number;                     // 开始时间戳
  endDate?: number;                       // 结束时间戳
  totalDays: number;                      // 总天数
}

export interface OrderTrendDataPoint {
  date: string;                           // 日期（YYYY-MM-DD格式）
  timestamp: number;                      // 时间戳
  orderCount: number;                     // 当日订单数
  completedCount: number;                 // 当日完成订单数
  processingCount: number;                // 当日处理中订单数
  cancelledCount: number;                 // 当日取消订单数
}

export interface DashboardOrderTrendResponse {
  dataPoints: OrderTrendDataPoint[];      // 趋势数据点
  summary: TrendSummary;                  // 趋势汇总信息
}

export interface TrendSummary {
  totalOrders: number;                    // 总订单数
  averageOrdersPerDay: number;            // 日均订单数
  peakDay?: OrderTrendDataPoint;          // 订单最多的一天
  growthRate: number;                     // 增长率（相比上一周期）
}

// 统计卡片数据类型
export interface StatsCardData {
  title: string;
  value: number | string;
  change?: number;                        // 变化百分比
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon?: string;
  description?: string;
}
