import { apiClient } from "@/lib/apiClient"
import type { UserLoginRequest, UserProfileResponse } from "./user-model"




export const UserApi = {
    userLogin: async (request: UserLoginRequest): Promise<string> => {
        return apiClient.post<string>({
            url: "/api/users/login",
            data: request
        })
    },

    userLogout: async (): Promise<void> => {
        return apiClient.post<void>({
            url: "/api/users/logout"
        })
    },

    profile: async (): Promise<UserProfileResponse> => {
        return apiClient.get<UserProfileResponse>({
            url: "/api/users/profile"
        })
    }
}