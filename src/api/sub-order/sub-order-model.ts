// 子订单相关的 TypeScript 类型定义

export interface SubOrderPageRequest {
  page: number;
  size: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  orderNo?: string;
  customerOrderNo?: string;
  start?: number;  // 开始时间戳（毫秒）
  end?: number;    // 结束时间戳（毫秒）
  status?: string; // 状态筛选，支持逗号分隔的多个状态
}

export interface RecipientInfo {
  userName?: string;
  receiverName?: string;
  country?: string;
  state?: string;
  city?: string;
  address1?: string;
  address2?: string;
  postcode?: string;
  phone?: string;
  email?: string;
}

export interface ProductInfo {
  spu?: string;
  size?: string;
  color?: string;
  qty: number;
  spuId?: number;
  skuId?: number;
  weight: number;
  price: number;
  currency: string;
  name: string;
  cnName: string;
  title: string;
  supplierId?: number;
  supplierName?: string;
  detail?: string;
  customName?: string;
  material?: string;
  hsCode?: string;
}

export interface ShippingInfo {
  channel: 'SF' | 'YTO' | 'YT' | 'BZ' | 'CAINIAO';
  shipMethod?: string;
  wayBillRelation?: string;
  deliveryMethod?: 'STANDARD' | 'EXPRESS';
  waybillLabelUrl?: string;
}

// 优化后的响应类型 - 只包含前端需要的字段
export interface RecipientInfoResponse {
  userName?: string;
  receiverName?: string;
  country?: string;
  state?: string;
  city?: string;
  address1?: string;
  address2?: string;
  postcode?: string;
  phone?: string;
  email?: string;
}

export interface ProductInfoResponse {
  spu?: string;
  size?: string;
  color?: string;
  qty: number;
  name: string;
  title: string;
}

export interface ShippingInfoResponse {
  channel: string;
  shipMethod?: string;
  wayBillRelation?: string;
  deliveryMethod?: string;
  waybillLabelUrl?: string;
}

export type SubOrderStatus =
  | 'CREATED'
  | 'CANCELLED'
  | 'COMPLETED'
  | 'SPLIT'
  | 'SUPPLIER_MATCHED'
  | 'FAILED'
  | 'TRACKING_NOT_FOUND'
  | 'TRACKING_PRE_ADVICE_RECEIVED'
  | 'TRACKING_PICKED_UP'
  | 'TRACKING_IN_TRANSIT'
  | 'TRACKING_ARRIVED_DESTINATION_COUNTRY'
  | 'TRACKING_IN_CUSTOMS'
  | 'TRACKING_CUSTOMS_CLEARED'
  | 'TRACKING_ARRIVED_FOR_PICKUP'
  | 'TRACKING_OUT_FOR_DELIVERY'
  | 'TRACKING_DELIVERY_FAILED'
  | 'TRACKING_DELIVERED'
  | 'TRACKING_EXCEPTION'
  | 'TRACKING_RETURNED'
  | 'TRACKING_CANCELLED'
  | 'TRACKING_UNKNOWN';

export type WayBillStatus =
  | 'SF'
  | 'YTO'
  | 'YT'
  | 'BZ'
  | 'CAINIAO';

export type OrderSource = 'LUXOMS' | 'IMPORT';

export interface SubOrder {
  id: string;
  parentId: number;
  originId?: number;
  fileName?: string;
  orderNo?: string;
  googleSearch?: string;
  urls?: string;
  effectUrl?: string;
  designUrl?: string;
  recipient: RecipientInfo;
  product: ProductInfo;
  shipping: ShippingInfo;
  customerId: number;
  customerName: string;
  bizId: number;
  status: SubOrderStatus;
  waybillStatus?: WayBillStatus;
  orderDownloadTaskIds: number[];
  source: OrderSource;
  remark: string;
  failedReason?: string;
  customerOrderNo: string;
  createdAt: string;  // ISO 8601 格式
  updatedAt: string;  // ISO 8601 格式
  createdByName: string;
  updatedByName: string;
  createdBy: number;
  updatedBy: number;
}

// 优化后的子订单响应类型 - 只包含前端需要的字段
export interface SubOrderResponse {
  id: number;
  customerOrderNo: string;
  status: SubOrderStatus;
  displayStatus: string; // 用于前端显示的状态，SUPPLIER_MATCHED 和 FAILED 都显示为 PROCESSING
  recipient: RecipientInfoResponse;
  product: ProductInfoResponse;
  shipping: ShippingInfoResponse;
  createdAt: number;  // 时间戳（毫秒）
}

export interface SubOrderPageResponse {
  content: SubOrder[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  empty: boolean;
}

// 优化后的分页响应类型
export interface SubOrderPageResponseOptimized {
  content: SubOrderResponse[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  empty: boolean;
}

// 状态显示映射（已废弃，请使用 getSubOrderStatusLabel 函数）
// @deprecated 请使用 src/utils/sub-order-status.ts 中的 getSubOrderStatusLabel 函数
export const SubOrderStatusLabels: Record<SubOrderStatus, string> = {
  CREATED: '已创建',
  CANCELLED: '已取消',
  COMPLETED: '已完成',
  SPLIT: '已拆分',
  SUPPLIER_MATCHED: '处理中', // 与 FAILED 合并显示
  FAILED: '处理中', // 与 SUPPLIER_MATCHED 合并显示
  TRACKING_NOT_FOUND: '未找到物流信息',
  TRACKING_PRE_ADVICE_RECEIVED: '预报信息已接收',
  TRACKING_PICKED_UP: '已揽收',
  TRACKING_IN_TRANSIT: '运输中',
  TRACKING_ARRIVED_DESTINATION_COUNTRY: '已到达目的国',
  TRACKING_IN_CUSTOMS: '清关中',
  TRACKING_CUSTOMS_CLEARED: '已清关',
  TRACKING_ARRIVED_FOR_PICKUP: '到达待取',
  TRACKING_OUT_FOR_DELIVERY: '派送中',
  TRACKING_DELIVERY_FAILED: '派送失败',
  TRACKING_DELIVERED: '已签收',
  TRACKING_EXCEPTION: '异常',
  TRACKING_RETURNED: '已退回',
  TRACKING_CANCELLED: '已取消',
  TRACKING_UNKNOWN: '未知状态'
};

// 渠道显示映射
export const WaybillChannelLabels: Record<string, string> = {
  SF: '顺丰',
  YTO: '圆通',
  YT: '云途',
  BZ: '斑马',
  CAINIAO: '菜鸟'
};

// 订单来源显示映射
export const OrderSourceLabels: Record<OrderSource, string> = {
  LUXOMS: 'System Order',
  IMPORT: 'Import Order'
};
