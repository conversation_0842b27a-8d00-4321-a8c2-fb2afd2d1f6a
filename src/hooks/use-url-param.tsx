import { useSearchParams } from 'react-router';

const useSearchParamsManager = () => {
    const [searchParams, setSearchParams] = useSearchParams();

    const addParam = (key: string, value: string) => {
        if (value === null || value === undefined || value === '') {
            deleteParam(key);
        } else {
            setSearchParams(params => {
                params.set(key, value);
                return params;
            });
        }
    };

    const deleteParam = (key: string) => {
        setSearchParams(params => {
            params.delete(key);
            return params;
        });
    };

    const resetParams = () => {
        setSearchParams({});
    };

    const getParam = (key: string) => {
        return searchParams.get(key);
    };

    const containsParam = (key: string) => {
        return searchParams.has(key);
    };

    return {
        addParam,
        deleteParam,
        resetParams,
        getParam,
        searchParams,
        containsParam
    };
};

export default useSearchParamsManager;