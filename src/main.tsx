import React from "react"
import ReactDOM from "react-dom/client"

import "./index.css"
import "./lib/i18n" // 初始化i18n
import { RouterProvider } from "react-router"
import { router } from "./router/self-route.tsx"
import { ModalProvider } from "./components/modals/index.ts"
import { Toaster } from "sonner"

// const stagewiseConfig = {
//   plugins: [],
// }

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <Toaster />
    <ModalProvider />
    <RouterProvider router={router} />
  </React.StrictMode>,
)

// if (process.env.NODE_ENV === "development") {
//   const toolbarRootEl = document.createElement("div")
//   toolbarRootEl.id = "stagewise-toolbar-root"
//   document.body.appendChild(toolbarRootEl)
//   ReactDOM.createRoot(toolbarRootEl).render(
//     <React.StrictMode>
//       <StagewiseToolbar config={stagewiseConfig} />
//     </React.StrictMode>,
//   )
// }
