
import HomePage from "@/app/dash/home/<USER>";
import SubOrderPage from "@/app/dash/sub-order/page";
import Login from "@/app/login/page";
import MainLayout from "@/components/layout/main-layout";
import { createBrowserRouter, Navigate } from "react-router";
export const router = createBrowserRouter([
    {
        path: "/",
        Component: MainLayout,
        children: [
            { index: true, element: <Navigate to="/dashboard/home" replace /> },
            {
                path: "dashboard",
                children: [
                    { path: "home", Component: HomePage },
                    { path: "sub-order", Component: SubOrderPage },
                ]
            },
        ],
    },
    { path: "/login", Component: Login },
]);
