import { AppSidebar } from "@/components/layout/app-sidebar"
import {
    SidebarInset,
    SidebarProvider,
    SidebarTrigger,
} from "@/components/ui/sidebar"
import { Outlet } from "react-router";
import { AppBreadcrumb } from "./app-breadcrumb";
import { Bell } from "lucide-react";
import { ScrollArea } from "../ui/scroll-area";
import AuthProvider from "./auth-provider";
import LanguageSwitcher from "@/components/ui/language-switcher";

export default function MainLayout() {

    return (
        <AuthProvider>
            <SidebarProvider>
                <AppSidebar />
                <SidebarInset className="w-full flex flex-col h-screen">
                    <header className="z-50 flex h-12 shrink-0 items-center border-b border-zinc-200 bg-[#FAFAFA]">
                        <div className="flex flex-row items-center justify-between w-full h-full">
                            <div className="flex items-center gap-2 px-4">
                                <SidebarTrigger className="-ml-1.5" />
                                <AppBreadcrumb />
                            </div>
                            <div className="flex items-center gap-2 px-4">
                                <LanguageSwitcher variant="ghost" size="sm" showText={false} />
                                <Bell className="size-4 text-zinc-500 hover:text-zinc-900 hover:cursor-pointer" />
                            </div>
                        </div>
                    </header>
                    <div className="flex-1 overflow-hidden">
                        <ScrollArea className="h-full">
                            <div className="px-6 py-4 transition-opacity delay-200 duration-200">
                                <Outlet />
                            </div>
                        </ScrollArea>
                    </div>
                </SidebarInset>
            </SidebarProvider>
        </AuthProvider>
    )
}
