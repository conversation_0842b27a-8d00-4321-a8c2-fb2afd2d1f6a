import { User<PERSON><PERSON> } from "@/api/user/user-api";
import { Loading } from "@/components/ui/loading";
import { userState } from "@/state/user-state";
import { useSetAtom } from "jotai";
import { type ReactNode, useEffect, useState } from "react";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";


interface AuthProviderProps {
    children: ReactNode
}


export default function AuthProvider({ children }: AuthProviderProps) {
    const { t } = useTranslation();
    const setUser = useSetAtom(userState);
    const [isInitializing, setIsInitializing] = useState(true);


    useEffect(() => {
        Promise.all([
            UserApi.profile().then(setUser),
        ]).catch((error) => {
            toast.error(t('messages.getUserInfoFailed', { message: error.message }))
        }).finally(() => {
            setIsInitializing(false)
        });
    }, [t]);


    if (isInitializing) {
        return <Loading />
    }

    return (
        <>{children}</>
    )
}