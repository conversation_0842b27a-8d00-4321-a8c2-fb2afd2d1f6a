import * as React from "react"
import { type LucideIcon } from "lucide-react"

import {
    SidebarGroup,
    SidebarGroupContent,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from "@/components/ui/sidebar"
import { Badge } from "../ui/badge"

export function NavSecondary({
    items,
    ...props
}: {
    items: {
        title: string
        url: string
        icon: LucideIcon
        keyboardShortcut?: string
        action?: () => void
    }[]
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
    // 添加键盘事件监听
    React.useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            items.forEach((item) => {
                if (item.keyboardShortcut && item.action) {
                    const keys = item.keyboardShortcut.toLowerCase().split(' ');
                    const modifierKeys = {
                        ctrl: event.ctrlKey,
                        shift: event.shiftKey,
                        alt: event.altKey,
                        meta: event.metaKey,
                    };

                    // 检查是否所有修饰键都匹配
                    const modifiersMatch = keys.every(key => {
                        if (key === 'ctrl') return modifierKeys.ctrl;
                        if (key === 'shift') return modifierKeys.shift;
                        if (key === 'alt') return modifierKeys.alt;
                        if (key === 'meta' || key === 'cmd' || key === '⌘') return modifierKeys.meta;
                        return true;
                    });

                    // 检查主键是否匹配
                    const mainKey = keys.find(key => !['ctrl', 'shift', 'alt', 'meta', 'cmd', '⌘'].includes(key));
                    const mainKeyMatch = mainKey ? event.key.toLowerCase() === mainKey.toLowerCase() : false;

                    if (modifiersMatch && mainKeyMatch) {
                        event.preventDefault();
                        item.action();
                    }
                }
            });
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [items]);

    return (
        <SidebarGroup {...props}>
            <SidebarGroupContent>
                <SidebarMenu>
                    {items.map((item) => (
                        <SidebarMenuItem key={item.title}>
                            <SidebarMenuButton asChild size="sm" onClick={item.action}>
                                <div className="flex flex-row justify-between w-full items-center">
                                    <div className="flex flex-row items-center gap-1">
                                        <item.icon className="text-sidebar-foreground/70 h-4 w-4" />
                                        <span>{item.title}</span>
                                    </div>
                                    {item.keyboardShortcut && (
                                        <Badge variant='outline' className="text-xs text-sidebar-foreground/70">
                                            {item.keyboardShortcut}
                                        </Badge>
                                    )}
                                </div>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    ))}
                </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup >
    )
}
