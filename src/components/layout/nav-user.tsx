"use client"

import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>g<PERSON>ut,
    <PERSON>rk<PERSON>,
} from "lucide-react"

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar,
} from "@/components/ui/sidebar"
import { userState } from "@/state/user-state"
import { useAtomValue } from "jotai"
import { UserApi } from "@/api/user/user-api"

export function NavUser() {
    const { isMobile } = useSidebar()

    const user = useAtomValue(userState);

    return (
        <SidebarMenu>
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className="data-[state=open]:bg-zinc-100 data-[state=open]:text-zinc-900"
                        >
                            <div className="flex size-6 items-center justify-center ">
                                <span className="flex shrink-0 items-center justify-center overflow-hidden shadow-borders-base rounded-full h-6 w-6 bg-white border border-zinc-200 shadow-zinc-200 shadow-xl">
                                    <span className="aspect-square object-cover object-center rounded-full size-5 bg-zinc-100 text-black/80 pointer-events-none flex select-none items-center justify-center">
                                        T
                                    </span>
                                </span>
                            </div>
                            <div className="grid flex-1 text-left text-sm leading-tight">
                                <span className="truncate font-medium text-[12px]">{user?.name}</span>
                            </div>
                            <Ellipsis className="ml-auto size-4 text-zinc-500" />
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                        side={isMobile ? "bottom" : "top"}
                        align="end"
                        sideOffset={4}
                    >
                        <DropdownMenuLabel className="p-0 font-normal">
                            <div className="flex items-center gap-4 px-1 py-1.5 text-left text-sm">
                                <span className="flex shrink-0 items-center justify-center overflow-hidden shadow-borders-base rounded-full h-6 w-6 bg-white border border-zinc-200 shadow-zinc-200 shadow-xl">
                                    <span className="aspect-square object-cover object-center rounded-full size-5 bg-zinc-100 text-black/80 pointer-events-none flex select-none items-center justify-center">
                                        T
                                    </span>
                                </span>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    <span className="truncate font-medium text-zinc-900">{user?.name}</span>
                                    <span className="truncate text-xs text-zinc-500">{user?.account}</span>
                                </div>
                            </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                            <DropdownMenuItem>
                                <Sparkles />
                                升级到专业版
                            </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                            <DropdownMenuItem >
                                <BadgeCheck />
                                账户
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                                <Bell />
                                通知
                            </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => UserApi.userLogout()}>
                            <LogOut />
                            退出
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </SidebarMenuItem>
        </SidebarMenu>
    )
}
