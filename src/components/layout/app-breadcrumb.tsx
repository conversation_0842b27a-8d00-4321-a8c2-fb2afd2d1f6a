import React, { memo } from "react";
import { useLocation } from "react-router";
import { useTranslation } from "react-i18next";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

function RawAppBreadcrumb() {
    const { pathname } = useLocation();
    const { t } = useTranslation();

    // 获取翻译文本的辅助函数
    const getBreadcrumbText = (segment: string): string => {
        // 首先尝试从 breadcrumb 命名空间获取翻译
        const breadcrumbKey = `breadcrumb.${segment}`;
        const breadcrumbText = t(breadcrumbKey);

        // 如果翻译键不存在，t() 会返回键本身，我们检查是否等于键
        if (breadcrumbText !== breadcrumbKey) {
            return breadcrumbText;
        }

        // 如果 breadcrumb 命名空间没有，尝试从 navigation 命名空间获取
        const navigationKey = `navigation.${segment}`;
        const navigationText = t(navigationKey);

        if (navigationText !== navigationKey) {
            return navigationText;
        }

        // 如果都没有找到翻译，返回格式化的段名
        return segment.charAt(0).toUpperCase() + segment.slice(1);
    };

    // 处理路径生成面包屑导航
    const generateBreadcrumbs = () => {
        // 移除第一个斜杠并分割路径
        const pathSegments = pathname.replace(/^\//, '').split('/').filter(Boolean);

        // 如果路径为空，则显示首页
        if (pathSegments.length === 0) {
            return (
                <BreadcrumbItem>
                    <BreadcrumbPage>{t('breadcrumb.home')}</BreadcrumbPage>
                </BreadcrumbItem>
            );
        }

        return pathSegments.map((segment: string, index: number) => {
            // 构建当前段的完整路径
            const path = '/' + pathSegments.slice(0, index + 1).join('/');

            // 获取翻译文本
            const displayText = getBreadcrumbText(segment);

            // 最后一个段落使用 BreadcrumbPage
            if (index === pathSegments.length - 1) {
                return (
                    <BreadcrumbItem key={path}>
                        <BreadcrumbPage className="!text-zinc-500">{displayText}</BreadcrumbPage>
                    </BreadcrumbItem>
                );
            }

            // 其他段落使用 BreadcrumbLink
            return (
                <React.Fragment key={path}>
                    <BreadcrumbItem>
                        <BreadcrumbLink href={path}>{displayText}</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                </React.Fragment>
            );
        });
    };

    return (
        <Breadcrumb>
            <BreadcrumbList>
                {generateBreadcrumbs()}
            </BreadcrumbList>
        </Breadcrumb>
    );
}


export const AppBreadcrumb = memo(RawAppBreadcrumb)