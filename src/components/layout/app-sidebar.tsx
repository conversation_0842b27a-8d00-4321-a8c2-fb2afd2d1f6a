"use client"

import * as React from "react"
import {
    BookOpen,
    SquareTerminal,
    Upload,
} from "lucide-react"
import { useTranslation } from "react-i18next"


import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
} from "@/components/ui/sidebar"
import { NavMain } from "./nav-main"
import { NavSecondary } from "./nav-secondary"
import { NavUser } from "./nav-user"
import { TeamSwitcher } from "./team-switcher"
import { Separator } from "../ui/separator"

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const { t } = useTranslation()

    const data = {
        user: {
            name: "shadcn",
            email: "<EMAIL>",
            avatar: "/avatars/shadcn.jpg",
        },
        navMain: [
            {
                title: t('navigation.dashboard'),
                url: "/dashboard/home",
                icon: SquareTerminal,
            },
            {
                title: t('navigation.subOrder'),
                url: "/dashboard/sub-order",
                icon: BookOpen,
            },
        ],
        navSecondary: [
            {
                title: t('navigation.createOrganization'),
                url: "#",
                icon: Upload,
                keyboardShortcut: "⌘ G",
                action: () => {
                    // pushModal('CreateSecretModal')
                }
            },
        ],
    }
    return (
        <Sidebar variant="inset" {...props}>
            <SidebarHeader>
                <TeamSwitcher />
            </SidebarHeader>
            <Separator className="bg-transparent border-t border-dashed border-border" />
            <SidebarContent>
                <NavMain items={data.navMain} />
                <NavSecondary items={data.navSecondary} className="mt-auto" />
            </SidebarContent>
            <Separator className="bg-transparent border-t border-dashed border-border" />
            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar >
    )
}
