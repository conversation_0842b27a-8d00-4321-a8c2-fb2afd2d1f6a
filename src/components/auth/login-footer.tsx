import React from 'react';
import { Globe } from 'lucide-react';

interface FooterProps {
    currentLanguage?: string;
    onLanguageChange?: (language: string) => void;
}

const LoginFooter: React.FC<FooterProps> = ({
    currentLanguage = "ENG",
    onLanguageChange = () => { }
}) => {
    const [showLanguages, setShowLanguages] = React.useState(false);

    const languages = [
        { code: "ENG", label: "English" },
        { code: "NL", label: "Dutch" },
        { code: "DE", label: "German" },
        { code: "FR", label: "French" },
    ];

    return (
        <div className="flex justify-between items-center py-4 px-8 text-sm text-gray-500">
            <div>
                &copy; {new Date().getFullYear()} Ticketapp
            </div>

            <div className="relative">
                <button
                    className="flex items-center gap-1.5 hover:text-gray-700 transition-colors"
                    onClick={() => setShowLanguages(!showLanguages)}
                >
                    <Globe size={16} />
                    {currentLanguage}
                    <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 1L5 5L9 1" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                </button>

                {showLanguages && (
                    <div className="absolute bottom-full mb-1 right-0 bg-white shadow-lg rounded-md py-1 min-w-32 z-10">
                        {languages.map((lang) => (
                            <button
                                key={lang.code}
                                className={`w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors
                ${currentLanguage === lang.code ? 'text-indigo-700 font-medium' : 'text-gray-700'}`}
                                onClick={() => {
                                    onLanguageChange(lang.code);
                                    setShowLanguages(false);
                                }}
                            >
                                {lang.label}
                            </button>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default LoginFooter;