import React from 'react';
import { Ticket } from 'lucide-react';

const PromotionalSide: React.FC = () => {
    return (
        <div className="bg-indigo-800/90 backdrop-blur-xl text-white relative overflow-hidden h-full w-full p-10 flex flex-col justify-between rounded-2xl">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-full h-full overflow-hidden">
                <div className="absolute top-[30%] right-[-10%] w-[90%] h-[70%] bg-blue-500/30 rounded-[40%] blur-3xl"></div>
                <div className="absolute bottom-[-10%] right-[20%] w-[60%] h-[60%] bg-indigo-400/40 rounded-[40%] blur-3xl"></div>
            </div>

            {/* Content */}
            <div className="relative z-10">
                <div className="flex items-center mb-6">
                    <div className="bg-white/90 backdrop-blur-sm p-2 rounded-xl mr-3 shadow-lg">
                        <Ticket className="text-indigo-800" size={24} />
                    </div>
                    <h1 className="text-3xl font-bold tracking-tight">图片下载器</h1>
                </div>
                <div className="mt-2">
                    <h2 className="text-2xl md:text-3xl font-light tracking-wide">
                        从网上下载图片从未如此简单。
                    </h2>
                </div>
            </div>

            {/* Ticket illustration */}
            <div className="relative z-10 mt-25 flex justify-center">
                <div className="relative">
                    <div className="w-72 h-56 bg-blue-500/60 backdrop-blur-xl rounded-2xl rotate-12 absolute -top-6 -right-12 
          shadow-xl animate-float-slow"></div>
                    <div className="w-80 h-64 bg-blue-600/60 backdrop-blur-xl rounded-2xl shadow-xl animate-float"></div>
                </div>
            </div>

            {/* Footer */}
            <div className="mt-auto relative z-10 flex justify-between">
                <div className="backdrop-blur-sm bg-white/10 p-4 rounded-xl">
                    <h3 className="font-semibold mb-2">获取访问权限</h3>
                    <p className="text-sm text-indigo-100">
                        注册账号开始使用<br />程序.
                    </p>
                </div>
                <div className="backdrop-blur-sm bg-white/10 p-4 rounded-xl text-right">
                    <h3 className="font-semibold mb-2">有问题？</h3>
                    <p className="text-sm text-indigo-100">
                        联系我们<a href="mailto:<EMAIL>" className="underline font-medium"><EMAIL></a>或<br />
                        拨打 <span className="whitespace-nowrap font-medium">+86 18257108806</span>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default PromotionalSide;