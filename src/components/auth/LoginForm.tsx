import React, { useState } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { Checkbox } from '../ui/checkbox';

interface LoginFormProps {
    onSubmit: (email: string, password: string, remember: boolean) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit }) => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [rememberMe, setRememberMe] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit(username, password, rememberMe);
    };

    return (
        <div className="w-full max-w-lg px-6 py-8 sm:px-8 space-y-6">
            <div className="flex flex-col items-center mb-8">
                <div className="rounded-full bg-gradient-to-br from-indigo-700 to-indigo-900 p-3 mb-4 shadow-xl">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4 6C4 4.89543 4.89543 4 6 4H18C19.1046 4 20 4.89543 20 6V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V6Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                </div>
            </div>

            <div className="mb-2 text-center">
                <h2 className="text-3xl font-bold text-gray-800 tracking-tight">登录账号</h2>
            </div>

            <p className="text-gray-600 mb-8 text-center">输入你的账号密码登录</p>

            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-6 backdrop-blur-sm bg-gradient-to-br from-white/50 to-white/30 p-6 rounded-xl shadow-xl py-8">
                    <div>
                        <label htmlFor="username" className="block text-sm font-semibold text-gray-700 mb-1.5 tracking-wide">
                            账号<span className="text-red-500">*</span>
                        </label>
                        <div className="relative">
                            <input
                                id="username"
                                type="text"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                required
                                className="w-full px-4 py-2.5 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg
                focus:ring-2 focus:ring-indigo-600/30 focus:border-indigo-600 transition-colors shadow"
                                placeholder="admin"
                            />
                        </div>
                    </div>

                    <div>
                        <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-1.5 tracking-wide">
                            密码<span className="text-red-500">*</span>
                        </label>
                        <div className="relative">
                            <input
                                id="password"
                                type={showPassword ? "text" : "password"}
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                required
                                className="w-full pl-4 pr-10 py-2.5 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg
                focus:ring-2 focus:ring-indigo-600/30 focus:border-indigo-600 transition-colors shadow"
                                placeholder="••••••••"
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                            >
                                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                            </button>
                        </div>
                    </div>
                </div>

                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center">
                        <Checkbox
                            id="remember"
                            checked={rememberMe}
                            onCheckedChange={(checked) => setRememberMe(checked === "indeterminate" ? false : checked)}
                        />

                        <label htmlFor="remember" className="ml-2 block text-sm text-gray-700 tracking-wide">
                            记住我
                        </label>
                    </div>
                    <a
                        href="#"
                        className="text-sm text-indigo-600 hover:text-indigo-700 font-semibold transition-colors tracking-wide"
                    >
                        忘记密码？
                    </a>
                </div>

                <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-indigo-600 to-indigo-800 hover:from-indigo-700 hover:to-indigo-900 text-white py-3 px-4 rounded-lg
          focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-300 ease-in-out shadow-xl
          font-semibold tracking-wide text-base hover:scale-105 hover:shadow-2xl hover:cursor-pointer"
                >
                    登录
                </button>
            </form>
        </div>
    );
};

export default LoginForm;