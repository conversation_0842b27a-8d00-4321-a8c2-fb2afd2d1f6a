import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Table } from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import React from 'react';
import { motion } from 'framer-motion';
import { TableBody } from './components/TableBody';
import { TableHeader } from './components/TableHeader';
import { DataTablePagination } from './data-table-pagination';
import { useAutoTableHeight } from './hooks/useAutoTableHeight';
import { useTableData } from './hooks/useTableData';
import { useTableState } from './hooks/useTableState';
import { type DataTableProps } from './types';

export function DataTable<TData, TValue>({
    columns,
    onFetch,
    toolbar,
    floatingBar,
    advancedSearch,
    isNeedSelect = false,
    dependencies = [],
    isFixedHeader = false,
    containerHeight: customHeight,
    isHidePagination = false,
    className,
    defaultPageSize = 10,
}: DataTableProps<TData, TValue>) {
    const autoHeight = useAutoTableHeight();
    const finalHeight = customHeight || autoHeight;

    const tableId = React.useMemo(() => {
        return columns.map(c => c.id).join('-');
    }, [columns]);

    const {
        columnOrder,
        setColumnOrder,
        sorting,
        setSorting,
        columnFilters,
        setColumnFilters,
        columnVisibility,
        setColumnVisibility,
        pagination: { pageIndex, pageSize },
        setPagination,
        rowSelection,
        setRowSelection
    } = useTableState(columns, isNeedSelect, tableId, defaultPageSize);

    const [advancedFilters, setAdvancedFilters] = React.useState<Record<string, any>>({});

    const { data, rowCount, loading, isRefreshing } = useTableData(
        onFetch,
        sorting,
        columnFilters,
        pageIndex,
        pageSize,
        advancedFilters,
        dependencies
    );

    const table = useReactTable({
        data,
        columns: isNeedSelect
            ? [
                {
                    id: 'select',
                    header: ({ table }) => (
                        <div className="h-2 flex items-center justify-center">
                            <Checkbox
                                checked={table.getIsAllPageRowsSelected()}
                                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                                aria-label="Select all"
                            />
                        </div>
                    ),
                    cell: ({ row }) => (
                        <div className="h-2 flex items-center justify-center">
                            <Checkbox
                                checked={row.getIsSelected()}
                                onCheckedChange={(value) => row.toggleSelected(!!value)}
                                aria-label="Select row"
                            />
                        </div>
                    ),
                    enableSorting: false,
                    enableHiding: false,
                },
                ...columns,
            ]
            : columns,
        pageCount: Math.ceil(rowCount / pageSize),
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            pagination: {
                pageIndex,
                pageSize,
            },
            columnOrder,
            rowSelection,
        },
        onColumnOrderChange: setColumnOrder,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onPaginationChange: setPagination,
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        getCoreRowModel: getCoreRowModel(),
        manualPagination: true,
        manualFiltering: true,
        manualSorting: true,
    });

    const handleAdvancedSearch = (filters: Record<string, any>) => {
        setAdvancedFilters(filters);
        setPagination({ pageIndex: 0, pageSize });
    };

    const handleAdvancedReset = () => {
        setAdvancedFilters({});
        setPagination({ pageIndex: 0, pageSize });
    };

    return (
        <div className="text-[11px] sm:text-xs">
            {advancedSearch && (
                <>
                    <div className="p-2 sm:p-4">
                        {React.cloneElement(advancedSearch as React.ReactElement, {
                            // @ts-ignore
                            onSearch: handleAdvancedSearch,
                            onReset: handleAdvancedReset,
                        })}
                    </div>
                    <Separator />
                </>
            )}
            {toolbar && toolbar(table, tableId)}
            {floatingBar && table.getFilteredSelectedRowModel().rows.length > 0 && floatingBar(table)}
            <div className={cn("", className)}>
                <div className="relative">
                    {isRefreshing && (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="absolute inset-0 z-10 bg-background/50 backdrop-blur-[1px]"
                        />
                    )}
                    {isFixedHeader ? (
                        <ScrollArea className="relative w-full" style={{ height: finalHeight }}>
                            <Table className="w-full table-fixed">
                                <TableHeader table={table} isFixedHeader={true} />
                                <TableBody table={table} loading={loading} />
                            </Table>
                        </ScrollArea>
                    ) : (
                        <div className="relative w-full overflow-x-auto overflow-y-auto">
                            <Table className="w-full table-fixed">
                                <TableHeader table={table} />
                                <TableBody table={table} loading={loading} />
                            </Table>
                        </div>
                    )}
                </div>
            </div>
            {!isHidePagination && (
                <div className="mt-2 mb-1">
                    <DataTablePagination table={table} />
                </div>
            )}
        </div>
    );
}
