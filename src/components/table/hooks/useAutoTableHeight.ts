import React from 'react';

export function useAutoTableHeight() {
    const [height, setHeight] = React.useState("600px");

    React.useEffect(() => {
        function calculateHeight() {
            const viewportHeight = window.innerHeight;
            const reservedSpace = 280;
            const availableHeight = Math.max(viewportHeight - reservedSpace, 400);
            setHeight(`${availableHeight}px`);
        }

        calculateHeight();
        window.addEventListener('resize', calculateHeight);
        return () => window.removeEventListener('resize', calculateHeight);
    }, []);

    return height;
} 