import { columnOrder<PERSON>tom } from '@/state/table-state';
import { type ColumnFiltersState, type SortingState, type VisibilityState } from '@tanstack/react-table';
import { useAtom } from 'jotai';
import React from 'react';
import useSearchParamsManager from '@/hooks/use-url-param';

export function useTableState(columns: any[], isNeedSelect: boolean, tableId: string, defaultPageSize: number = 10) {
    const [columnOrderState] = useAtom(columnOrderAtom);
    const { getParam, addParam } = useSearchParamsManager();

    // 初始化 URL 参数（如果不存在的话）
    React.useEffect(() => {
        const currentPage = getParam('page');
        const currentSize = getParam('size');

        if (!currentPage) {
            addParam('page', '0');
        }
        if (!currentSize) {
            addParam('size', defaultPageSize.toString());
        }
    }, []); // 只在组件挂载时运行一次

    const [columnOrder, setColumnOrder] = React.useState<string[]>(() =>
        isNeedSelect ? ['select', ...columns.map(c => c.id!)] : columns.map(c => c.id!)
    );

    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});

    // 从 URL 参数获取分页状态
    const pageIndex = React.useMemo(() => {
        const pageParam = getParam('page');
        return pageParam ? parseInt(pageParam) : 0;
    }, [getParam]);

    const pageSize = React.useMemo(() => {
        const sizeParam = getParam('size');
        return sizeParam ? parseInt(sizeParam) : defaultPageSize;
    }, [getParam, defaultPageSize]);

    // 创建 setPagination 函数来更新 URL 参数
    const setPagination = React.useCallback((updater: any) => {
        if (typeof updater === 'function') {
            const currentPagination = { pageIndex, pageSize };
            const newPagination = updater(currentPagination);
            addParam('page', newPagination.pageIndex.toString()); // URL 中存储从 0 开始的页码
            addParam('size', newPagination.pageSize.toString());
        } else {
            addParam('page', updater.pageIndex.toString());
            addParam('size', updater.pageSize.toString());
        }
    }, [pageIndex, pageSize, addParam]);

    const [rowSelection, setRowSelection] = React.useState({});

    React.useEffect(() => {
        if (columnOrderState[tableId]) {
            setColumnOrder(columnOrderState[tableId]);
        }
    }, [columnOrderState, tableId]);

    return {
        columnOrder,
        setColumnOrder,
        sorting,
        setSorting,
        columnFilters,
        setColumnFilters,
        columnVisibility,
        setColumnVisibility,
        pagination: { pageIndex, pageSize },
        setPagination,
        rowSelection,
        setRowSelection
    };
} 