

/* 活动单元格样式 */
.edit-table-container .active-cell {
  position: relative;
  z-index: 10;
}

/* 编辑中的单元格样式 */
.edit-table-container [contenteditable] {
  outline: none;
  min-height: 1.5rem;
  cursor: text;
  caret-color: #3b82f6;
  padding: 0.5rem;
  width: 100%;
  height: 100%;
  white-space: pre-wrap;
  word-break: break-word;
  position: relative;
  z-index: 20;
  user-select: text;
}

.edit-table-container [contenteditable]:focus {
  background-color: white;
}

/* 防止表格单元格内容被选中 */
.edit-table-container .table-cell-content {
  user-select: none;
}
