import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as z from 'zod';

export type FieldType = 'input' | 'textarea' | 'select' | 'dateRange' | 'checkbox' | 'radio' | 'slider' | 'switch';

export interface FieldConfig {
    name: string;
    label: string;
    type: FieldType;
    placeholder?: string;
    options?: { value: string; label: string }[];
    min?: number;
    max?: number;
    step?: number;
    rows?: number;
    layout?: {
        colSpan?: number;
        rowSpan?: number;
        order?: number;
    };
}

const generateZodSchema = (fields: FieldConfig[]) => {
    const schemaObject: Record<string, z.ZodTypeAny> = {};
    fields.forEach(field => {
        switch (field.type) {
            case 'input':
            case 'textarea':
            case 'select':
            case 'radio':
                schemaObject[field.name] = z.string();
                break;
            case 'dateRange':
                schemaObject[field.name] = z.object({
                    from: z.date().optional(),
                    to: z.date().optional(),
                });
                break;
            case 'checkbox':
                schemaObject[field.name] = z.array(z.string());
                break;
            case 'switch':
                schemaObject[field.name] = z.boolean();
                break;
            case 'slider':
                schemaObject[field.name] = z.number();
                break;
        }
    });
    return z.object(schemaObject);
};

export type SearchFormValues = Record<string, any>;

interface AdvancedSearchToolbarProps {
    formFields: FieldConfig[];
    onSearch: (filters: SearchFormValues) => void;
    onReset: () => void;
    layout?: {
        columns?: number;
        gap?: string;
    };
}

export function AdvancedSearchToolbar({ formFields, onSearch, onReset, layout = { columns: 3, gap: '1rem' } }: AdvancedSearchToolbarProps) {
    const searchSchema = React.useMemo(() => generateZodSchema(formFields), [formFields]);

    const form = useForm<SearchFormValues>({
        resolver: zodResolver(searchSchema),
        defaultValues: React.useMemo(() => formFields.reduce((acc, field) => {
            switch (field.type) {
                case 'input':
                case 'textarea':
                case 'select':
                case 'radio':
                    acc[field.name] = '';
                    break;
                case 'dateRange':
                    acc[field.name] = { from: undefined, to: undefined };
                    break;
                case 'checkbox':
                    acc[field.name] = [];
                    break;
                case 'switch':
                    acc[field.name] = false;
                    break;
                case 'slider':
                    acc[field.name] = field.min || 0;
                    break;
            }
            return acc;
        }, {} as Record<string, any>), [formFields]),
    });

    const handleSubmit = (data: SearchFormValues) => {
        onSearch(data);
    };

    const handleReset = () => {
        form.reset();
        onReset();
    };

    const renderField = (field: FieldConfig) => {
        switch (field.type) {
            case 'input':
                return <Input placeholder={field.placeholder} {...form.register(field.name)} />;
            case 'textarea':
                return <Textarea
                    placeholder={field.placeholder}
                    {...form.register(field.name)}
                    rows={field.rows || 3}
                />;
            case 'select':
                return (
                    <Select onValueChange={(value) => form.setValue(field.name, value)} value={form.watch(field.name)}>
                        <SelectTrigger>
                            <SelectValue placeholder={field.label} />
                        </SelectTrigger>
                        <SelectContent>
                            {field.options?.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                );
            case 'dateRange':
                return (
                    <Controller
                        name={field.name}
                        control={form.control}
                        render={({ }) => (
                            // <DatePickerWithRange
                            //     value={value as DateRange}
                            //     onChange={onChange}
                            // />
                            <div></div>
                        )}
                    />
                );
            case 'checkbox':
                return (
                    <div className="space-y-2">
                        {field.options?.map((option) => (
                            <div key={option.value} className="flex items-center space-x-2">
                                <Checkbox
                                    id={`${field.name}-${option.value}`}
                                    {...form.register(field.name)}
                                    value={option.value}
                                />
                                <label htmlFor={`${field.name}-${option.value}`}>{option.label}</label>
                            </div>
                        ))}
                    </div>
                );
            case 'radio':
                return (
                    <RadioGroup onValueChange={(value) => form.setValue(field.name, value)} value={form.watch(field.name)}>
                        {field.options?.map((option) => (
                            <div key={option.value} className="flex items-center space-x-2">
                                <RadioGroupItem value={option.value} id={`${field.name}-${option.value}`} />
                                <label htmlFor={`${field.name}-${option.value}`}>{option.label}</label>
                            </div>
                        ))}
                    </RadioGroup>
                );
            case 'slider':
                return (
                    <Slider
                        min={field.min}
                        max={field.max}
                        step={field.step}
                        value={[form.watch(field.name)]}
                        onValueChange={(value) => form.setValue(field.name, value[0])}
                    />
                );
            case 'switch':
                return (
                    <div className="flex flex-col space-y-2">
                        <FormLabel>{field.label}</FormLabel>
                        <div className="flex items-center space-x-2">
                            <Switch
                                checked={form.watch(field.name)}
                                onCheckedChange={(checked) => form.setValue(field.name, checked)}
                            />
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    const sortedFields = React.useMemo(() =>
        [...formFields].sort((a, b) => (a.layout?.order || 0) - (b.layout?.order || 0)),
        [formFields]
    );

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                <div
                    style={{
                        display: 'grid',
                        gridTemplateColumns: `repeat(${layout.columns}, 1fr)`,
                        gap: layout.gap,
                    }}
                >
                    {sortedFields.map((field) => (
                        <FormField
                            key={field.name}
                            control={form.control}
                            name={field.name as any}
                            render={() => (
                                <FormItem
                                    style={{
                                        gridColumn: field.layout?.colSpan ? `span ${field.layout.colSpan}` : 'span 1',
                                        gridRow: field.layout?.rowSpan ? `span ${field.layout.rowSpan}` : 'auto',
                                    }}
                                >
                                    {/* {field.type !== 'switch' && <FormLabel>{field.label}</FormLabel>} */}
                                    <FormControl>{renderField(field)}</FormControl>
                                </FormItem>
                            )}
                        />
                    ))}
                </div>
                <div className="flex justify-between">
                    <div className="space-x-2">
                        <Button type="submit" size="sm">Search</Button>
                        <Button type="button" size="sm" variant="outline" onClick={handleReset}>
                            Reset
                        </Button>
                    </div>
                </div>
            </form>
        </Form>
    );
}