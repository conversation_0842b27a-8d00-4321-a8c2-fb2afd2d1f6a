import { type ColumnDef } from "@tanstack/react-table";
import React from "react";

declare module '@tanstack/table-core' {
    interface ColumnMeta<TData extends unknown, TValue> {
        enableSortingIcon?: boolean;
        title?: string;
        fixed?: 'left' | 'right';
        defaultSortDesc?: boolean;
    }
}

export interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    onFetch: (params: FetchParams) => Promise<FetchResponse<TData>>;
    toolbar?: (table: any, tableId: string) => React.ReactNode;
    floatingBar?: (table: any) => React.ReactNode;
    advancedSearch?: React.ReactNode;
    isNeedSelect?: boolean;
    dependencies?: any[];
    isFixedHeader?: boolean;
    containerHeight?: string;
    className?: string;
    isHidePagination?: boolean;
    defaultPageSize?: number;
}

export interface FetchParams {
    filters: Record<string, any>;
    sort?: string[];
    direction?: ('ASC' | 'DESC')[];
    pagination: { pageIndex: number; pageSize: number };
    advancedFilters?: Record<string, any>;
    searchParams: Record<string, any>;
}

export interface FetchResponse<TData> {
    content: TData[];
    total: number;
} 