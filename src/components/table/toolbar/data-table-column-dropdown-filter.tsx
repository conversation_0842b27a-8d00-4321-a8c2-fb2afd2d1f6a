import { Button } from "@/components/ui/button"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
    CommandSeparator
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import useSearchParamsManager from "@/hooks/use-url-param"
import { cn } from "@/lib/utils"
import { type Column } from "@tanstack/react-table"
import { CheckIcon, Filter } from "lucide-react"
import * as React from "react"

interface FilterOption {
    label: string
    value: string | number
    icon?: React.ComponentType<{ className?: string }>
}

interface DataTableColumnDropdownFilterProps<TData, TValue> {
    column?: Column<TData, TValue>
    title?: string
    options?: FilterOption[]
    fetchOptions?: () => Promise<FilterOption[]>
    useUrlParam?: boolean
    paramKey?: string
}

export function DataTableColumnDropdownFilter<TData, TValue>({
    column,
    title,
    options: initialOptions,
    fetchOptions,
    useUrlParam = false,
    paramKey,
}: DataTableColumnDropdownFilterProps<TData, TValue>) {
    const [options, setOptions] = React.useState<FilterOption[]>(initialOptions || [])
    const [loading, setLoading] = React.useState(false)
    const { getParam, addParam } = useSearchParamsManager();

    const selectedValues = React.useMemo(() => {
        if (useUrlParam && paramKey) {
            const paramValue = getParam(paramKey);
            return new Set(paramValue ? paramValue.split(',') : []);
        }
        return new Set(column?.getFilterValue() as string[]);
    }, [useUrlParam, paramKey, getParam, column]);

    React.useEffect(() => {
        if (useUrlParam && paramKey && column) {
            const paramValue = getParam(paramKey);
            if (paramValue) {
                const values = paramValue.split(',');
                column.setFilterValue(values);
            }
        }
    }, []);

    React.useEffect(() => {
        if (fetchOptions) {
            setLoading(true)
            fetchOptions()
                .then(newOptions => {
                    setOptions(newOptions)
                })
                .finally(() => {
                    setLoading(false)
                })
        }
    }, [fetchOptions])

    const handleSelect = React.useCallback(
        (value: string | number) => {
            if (!column) return

            const newSelectedValues = new Set(selectedValues)
            if (newSelectedValues.has(value.toString())) {
                newSelectedValues.delete(value.toString())
            } else {
                newSelectedValues.add(value.toString())
            }

            const filterValues = Array.from(newSelectedValues)

            column.setFilterValue(filterValues.length ? filterValues : undefined)

            if (useUrlParam && paramKey) {
                addParam(paramKey, filterValues.length ? filterValues.join(',') : '');
            }
        },
        [column, selectedValues, useUrlParam, paramKey, addParam]
    )

    const handleClear = React.useCallback(() => {
        if (!column) return;

        column.setFilterValue(undefined);

        if (useUrlParam && paramKey) {
            addParam(paramKey, '');
        }
    }, [column, useUrlParam, paramKey, addParam]);

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                        "h-7 w-7 p-0 hover:bg-muted",
                        selectedValues.size > 0 && "text-primary",
                    )}
                >
                    <Filter className="h-3 w-3" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0" align="end">
                <Command>
                    <CommandInput placeholder={`Search ${title}...`} />
                    <CommandList>
                        <CommandEmpty>No results found.</CommandEmpty>
                        <CommandGroup>
                            {loading ? (
                                <div className="p-2 text-sm text-muted-foreground">
                                    Loading...
                                </div>
                            ) : (
                                options.map((option) => {
                                    const isSelected = selectedValues.has(option.value.toString())
                                    return (
                                        <CommandItem
                                            key={option.value}
                                            onSelect={() => handleSelect(option.value)}
                                        >
                                            <div
                                                className={cn(
                                                    "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                                                    isSelected
                                                        ? "bg-primary text-primary-foreground"
                                                        : "opacity-50 [&_svg]:invisible"
                                                )}
                                            >
                                                <CheckIcon className={cn("h-4 w-4")} />
                                            </div>
                                            {option.icon && (
                                                <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                                            )}
                                            <span>{option.label}</span>
                                        </CommandItem>
                                    )
                                })
                            )}
                        </CommandGroup>
                        {selectedValues.size > 0 && (
                            <>
                                <CommandSeparator />
                                <CommandGroup>
                                    <CommandItem
                                        onSelect={handleClear}
                                        className="justify-center text-center"
                                    >
                                        Clear filter
                                    </CommandItem>
                                </CommandGroup>
                            </>
                        )}
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )
} 