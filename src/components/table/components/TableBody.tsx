import { TableCell, TableBody as UITableBody } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { flexRender } from '@tanstack/react-table';
import { EmptyState } from './EmptyState';
import { motion, AnimatePresence } from 'framer-motion';
import { TableSkeleton } from './TableSkeleton';

interface TableBodyProps {
    table: any;
    loading?: boolean;
}

export function TableBody({ table, loading }: TableBodyProps) {
    if (loading && !table.getRowModel().rows?.length) {
        return (
            <UITableBody>
                <TableSkeleton columns={table.getAllColumns().length} rows={table.getState().pagination.pageSize} />
            </UITableBody>
        );
    }

    return (
        <UITableBody>
            <AnimatePresence mode="wait">
                {table.getRowModel().rows?.length ? (
                    table.getRowModel().rows.map((row: any, index: number) => (
                        <motion.tr
                            key={row.id}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ 
                                duration: 0.2,
                                delay: index * 0.02,
                                ease: "easeOut"
                            }}
                            data-state={row.getIsSelected() && "selected"}
                            className={cn(
                                "group",
                                "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                            )}
                        >
                        {row.getVisibleCells().map((cell: any, index: number) => {
                            const isFixedLeft = cell.column.columnDef.meta?.fixed === 'left';
                            const isFixedRight = cell.column.columnDef.meta?.fixed === 'right';

                            return (
                                <TableCell
                                    className={cn(
                                        "px-4 group-hover:bg-muted text-zinc-500",
                                        isFixedLeft && "sticky left-0 z-20 bg-background",
                                        isFixedRight && "sticky right-0 z-20 bg-background",
                                        (isFixedLeft || isFixedRight) && "drop-shadow-md"
                                    )}
                                    style={{
                                        ...(isFixedLeft && {
                                            left: `${index * 80}px`,
                                        })
                                    }}
                                    key={cell.id}
                                >
                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                </TableCell>
                            );
                        })}
                        </motion.tr>
                    ))
                ) : (
                    <EmptyState colSpan={table.getAllColumns().length} />
                )}
            </AnimatePresence>
        </UITableBody>
    );
} 