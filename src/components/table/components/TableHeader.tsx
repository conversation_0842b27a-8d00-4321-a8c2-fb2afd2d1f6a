import { Button } from '@/components/ui/button';
import { TableHead, TableRow, TableHeader as UITableHeader } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { SortableContext, horizontalListSortingStrategy } from '@dnd-kit/sortable';
import { flexRender } from '@tanstack/react-table';
import { ArrowDownIcon, ArrowUpDownIcon, ArrowUpIcon } from 'lucide-react';

interface TableHeaderProps {
    table: any;
    isFixedHeader?: boolean;
}

export function TableHeader({ table, isFixedHeader }: TableHeaderProps) {
    return (
        <UITableHeader className={cn("bg-background", isFixedHeader && "sticky top-0 z-10")}>
            <SortableContext
                items={table.getState().columnOrder}
                strategy={horizontalListSortingStrategy}
            >
                {table.getHeaderGroups().map((headerGroup: any) => (
                    <TableRow key={headerGroup.id} className="group">
                        {headerGroup.headers.map((header: any, index: number) => {
                            const isFixedLeft = header.column.columnDef.meta?.fixed === 'left';
                            const isFixedRight = header.column.columnDef.meta?.fixed === 'right';

                            return (
                                <TableHead
                                    key={header.id}
                                    className={cn(
                                        "text-nowrap min-w-[80px] relative group-hover:bg-muted border-t text-[13px]",
                                        header.id === 'select' && "min-w-[50px] w-[50px]",
                                        isFixedHeader && "bg-background",
                                        isFixedLeft && "sticky left-0 z-20",
                                        isFixedRight && "sticky right-0 z-20",
                                        (isFixedLeft || isFixedRight) && "bg-background drop-shadow-md"
                                    )}
                                    style={{
                                        ...(isFixedLeft && {
                                            left: `${index * 80}px`,
                                        })
                                    }}
                                >
                                    {header.isPlaceholder ? null : (
                                        <div className="flex items-center h-full">
                                            <div className="flex items-center flex-1">
                                                <div className="flex-1">
                                                    {flexRender(header.column.columnDef.header, header.getContext())}
                                                </div>
                                                {header.column.getCanSort() && header.column.columnDef.meta?.enableSortingIcon && (
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="h-8 data-[state=open]:bg-accent ml-2"
                                                        onClick={() => {
                                                            const currentSort = header.column.getIsSorted();
                                                            const defaultDesc = header.column.columnDef.meta?.defaultSortDesc || false;

                                                            if (!currentSort) {
                                                                // 第一次点击：使用默认排序方向
                                                                header.column.toggleSorting(defaultDesc);
                                                            } else if (
                                                                (defaultDesc && currentSort === "desc") ||
                                                                (!defaultDesc && currentSort === "asc")
                                                            ) {
                                                                // 第二次点击：切换到相反方向
                                                                header.column.toggleSorting(!defaultDesc);
                                                            } else {
                                                                // 第三次点击：清除排序
                                                                header.column.clearSorting();
                                                            }
                                                        }}
                                                    >
                                                        {header.column.getIsSorted() === "desc" ? (
                                                            <ArrowDownIcon className="h-4 w-4" />
                                                        ) : header.column.getIsSorted() === "asc" ? (
                                                            <ArrowUpIcon className="h-4 w-4" />
                                                        ) : (
                                                            <ArrowUpDownIcon className="h-4 w-4" />
                                                        )}
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </TableHead>
                            );
                        })}
                    </TableRow>
                ))}
            </SortableContext>
        </UITableHeader>
    );
} 