import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeftIcon, ChevronRightIcon, DoubleArrowLeftIcon, DoubleArrowRightIcon, } from "@radix-ui/react-icons";
import { type Table } from "@tanstack/react-table";
import useSearchParamsManager from "@/hooks/use-url-param";
import { useTranslation } from "react-i18next";

interface DataTablePaginationProps<TData> {
    table: Table<TData>
}

export function DataTablePagination<TData>({
    table,
}: DataTablePaginationProps<TData>) {
    const { addParam } = useSearchParamsManager();
    const { t } = useTranslation();

    // 直接更新 URL 参数的分页处理函数
    const handlePageSizeChange = (value: string) => {
        const newPageSize = Number(value);
        addParam('size', newPageSize.toString());
        addParam('page', '0'); // 重置到第一页
        table.setPageSize(newPageSize);
    };

    const handleGoToFirstPage = () => {
        addParam('page', '0');
        table.setPageIndex(0);
    };

    const handlePreviousPage = () => {
        const newPage = table.getState().pagination.pageIndex;
        addParam('page', newPage.toString());
        table.previousPage();
    };

    const handleNextPage = () => {
        const newPage = table.getState().pagination.pageIndex + 1;
        addParam('page', newPage.toString());
        table.nextPage();
    };

    const handleGoToLastPage = () => {
        const lastPageIndex = table.getPageCount() - 1;
        addParam('page', lastPageIndex.toString());
        table.setPageIndex(lastPageIndex);
    };

    return (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 px-4 py-2">
            <div className="flex-1 text-xs text-muted-foreground text-center sm:text-left">
                {t('pagination.rowsSelected', { count: table.getFilteredSelectedRowModel().rows.length })} / {t('pagination.totalRows', { count: table.getFilteredRowModel().rows.length })}
            </div>
            <div className="flex flex-col sm:flex-row items-center gap-4 sm:space-x-6 lg:space-x-8">
                <div className="flex items-center space-x-2">
                    <p className="text-xs text-muted-foreground">{t('pagination.rowsPerPage')}</p>
                    <Select
                        value={`${table.getState().pagination.pageSize}`}
                        onValueChange={handlePageSizeChange}
                    >
                        <SelectTrigger className="h-8 w-[70px] sm:w-[100px]">
                            <SelectValue placeholder={table.getState().pagination.pageSize} />
                        </SelectTrigger>
                        <SelectContent side="top">
                            {[10, 20, 30, 50, 100].map((pageSize) => (
                                <SelectItem key={pageSize} value={`${pageSize}`}>
                                    {pageSize}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex w-[100px] items-center justify-center text-xs text-muted-foreground text-nowrap">
                    {t('pagination.page', { current: table.getState().pagination.pageIndex + 1 })} {t('pagination.totalPages', { total: table.getPageCount() === 0 ? 1 : table.getPageCount() })}
                </div>
                <div className="flex items-center space-x-2">
                    <Button
                        variant="outline"
                        className="hidden sm:flex h-8 w-8 p-0"
                        onClick={handleGoToFirstPage}
                        disabled={!table.getCanPreviousPage()}
                    >
                        <span className="sr-only">{t('pagination.goToFirstPage')}</span>
                        <DoubleArrowLeftIcon className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        className="h-9 sm:h-8 w-9 sm:w-8 p-0"
                        onClick={handlePreviousPage}
                        disabled={!table.getCanPreviousPage()}
                    >
                        <span className="sr-only">{t('pagination.previousPage')}</span>
                        <ChevronLeftIcon className="h-5 sm:h-4 w-5 sm:w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        className="h-9 sm:h-8 w-9 sm:w-8 p-0"
                        onClick={handleNextPage}
                        disabled={!table.getCanNextPage()}
                    >
                        <span className="sr-only">{t('pagination.nextPage')}</span>
                        <ChevronRightIcon className="h-5 sm:h-4 w-5 sm:w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        className="hidden sm:flex h-8 w-8 p-0"
                        onClick={handleGoToLastPage}
                        disabled={!table.getCanNextPage()}
                    >
                        <span className="sr-only">{t('pagination.goToLastPage')}</span>
                        <DoubleArrowRightIcon className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </div>
    )
}