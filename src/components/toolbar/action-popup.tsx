"use client"

import { useEffect } from "react"
import type { PopupAction } from "./types/toolbar-types"

interface ActionPopupProps {
    isOpen: boolean
    onClose: () => void
    actions: PopupAction[]
}

export default function ActionPopup({ isOpen, onClose, actions }: ActionPopupProps) {
    useEffect(() => {
        if (isOpen) {
            const handleEscape = (e: KeyboardEvent) => {
                if (e.key === "Escape") {
                    onClose()
                }
            }
            document.addEventListener("keydown", handleEscape)
            return () => document.removeEventListener("keydown", handleEscape)
        }
    }, [isOpen, onClose])

    if (!isOpen) return null

    return (
        <>
            <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40" onClick={onClose} />

            <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50">
                <div className="bg-white rounded-2xl shadow-[0_16px_48px_rgba(0,0,0,0.2)] border border-gray-200 overflow-hidden animate-in slide-in-from-bottom-4 duration-200">
                    <div className="p-2">
                        {actions.map((action) => (
                            <button
                                key={action.id}
                                onClick={() => {
                                    action.onClick()
                                    onClose()
                                }}
                                className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors rounded-xl ${action.variant === "destructive" ? "text-red-600" : "text-gray-900"
                                    }`}
                            >
                                <div className={`flex-shrink-0 ${action.variant === "destructive" ? "text-red-500" : "text-gray-500"}`}>
                                    {action.icon}
                                </div>
                                <span className="text-sm font-medium">{action.label}</span>
                            </button>
                        ))}
                    </div>
                </div>
            </div>
        </>
    )
}
