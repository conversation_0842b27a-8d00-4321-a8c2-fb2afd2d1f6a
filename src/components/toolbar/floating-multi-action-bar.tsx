"use client"

import { useState, useEffect } from "react"
import type { FloatingAction } from "./types/toolbar-types"
import ActionPopup from "./action-popup"

interface FloatingMultiActionBarProps {
    selectedCount: number
    selectedItems: any[]
    actions: FloatingAction[]
    className?: string
}

export default function FloatingMultiActionBar({
    selectedCount,
    actions,
    className = "",
    selectedItems,
}: FloatingMultiActionBarProps) {
    const [activePopup, setActivePopup] = useState<string | null>(null)

    // 添加键盘快捷键监听
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            // 检查是否有按下快捷键
            const matchedAction = actions.find(action => {
                // 简单匹配快捷键，假设快捷键格式为单个字符
                return action.shortcut?.toLowerCase() === event.key.toLowerCase();
            });

            if (matchedAction) {
                if (matchedAction.popupActions && matchedAction.popupActions.length > 0) {
                    setActivePopup(activePopup === matchedAction.id ? null : matchedAction.id);
                } else {
                    matchedAction.onClick(selectedItems);
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);

        // 组件卸载时移除事件监听
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [actions, activePopup, selectedItems]);

    const handleActionClick = (action: FloatingAction) => {
        if (action.popupActions && action.popupActions.length > 0) {
            setActivePopup(activePopup === action.id ? null : action.id)
        } else {
            action.onClick(selectedItems)
        }
    }

    const closePopup = () => {
        setActivePopup(null)
    }

    return (
        <>
            <div className={`fixed bottom-12 left-1/2 transform -translate-x-1/2 z-50 ${className}`}>
                <div className="bg-black/95 backdrop-blur-md rounded-full shadow-[0_8px_32px_rgba(0,0,0,0.3)] border border-white/15">
                    <div className="flex items-center">
                        {/* Selected count */}
                        <div className="text-white/70 text-xs font-medium px-4 py-2.5 border-r border-white/15"> 已选择 {selectedCount} 行</div>

                        {/* Actions */}
                        <div className="flex h-10">
                            {actions.map((action, index) => (
                                <div
                                    key={action.id}
                                    className="flex items-center h-full"
                                >
                                    {index > 0 && (
                                        <div className="h-full flex items-center">
                                            <div className="w-px h-full bg-white/15"></div>
                                        </div>
                                    )}
                                    <div
                                        className="px-4 h-full flex items-center justify-center cursor-pointer hover:bg-white/20 transition-colors"
                                        onClick={() => handleActionClick(action)}
                                    >
                                        <div className="flex items-center space-x-2">
                                            <span className="text-white text-xs font-medium tracking-[0.2em]">{action.label}</span>
                                            <div className="flex h-3 w-3 items-center justify-center rounded text-primary-foreground">
                                                <div className="p-[1px] rounded flex flex-row items-center justify-center border border-zinc-600 bg-[#27272A]">
                                                    <span className="flex shrink-0 items-center justify-center overflow-hidden shadow-borders h-4 w-4 rounded bg-[#27272A]">
                                                        <span className="aspect-square object-cover object-center rounded bg-ui-bg-component-hover text-zinc-300/90 pointer-events-none flex select-none items-center justify-center text-xs">
                                                            {action.shortcut}
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div >
            </div >

            {/* Action Popups */}
            {
                actions.map(
                    (action) =>
                        action.popupActions &&
                        action.popupActions.length > 0 && (
                            <ActionPopup
                                key={action.id}
                                isOpen={activePopup === action.id}
                                onClose={closePopup}
                                actions={action.popupActions}
                            />
                        ),
                )
            }
        </>
    )
}
