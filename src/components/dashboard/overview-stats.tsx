import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Minus, Package, CheckCircle, Clock, XCircle } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { DashboardOverviewResponse, StatsCardData } from "@/api/dashboard/dashboard-model";

interface OverviewStatsProps {
  data: DashboardOverviewResponse;
  loading?: boolean;
}

export default function OverviewStats({ data, loading }: OverviewStatsProps) {
  const { t } = useTranslation();
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
              <div className="h-4 w-4 bg-gray-200 rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-24"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statsCards: StatsCardData[] = [
    {
      title: t('dashboard.charts.overviewStats.totalOrders'),
      value: data.totalOrders,
      icon: "Package",
      description: t('dashboard.charts.overviewStats.descriptions.totalOrders', { days: data.dateRange.totalDays }),
    },
    {
      title: t('dashboard.charts.overviewStats.completedOrders'),
      value: data.completedOrders,
      change: data.completionRate,
      changeType: data.completionRate >= 80 ? 'increase' : data.completionRate >= 60 ? 'neutral' : 'decrease',
      icon: "CheckCircle",
      description: t('dashboard.charts.overviewStats.descriptions.completedOrders', { rate: data.completionRate.toFixed(1) }),
    },
    {
      title: t('dashboard.charts.overviewStats.processingOrders'),
      value: data.processingOrders,
      icon: "Clock",
      description: t('dashboard.charts.overviewStats.descriptions.processingOrders'),
    },
    {
      title: t('dashboard.charts.overviewStats.recentOrders'),
      value: data.recentOrdersCount,
      icon: "TrendingUp",
      description: t('dashboard.charts.overviewStats.descriptions.recentOrders'),
    },
  ];

  const getIcon = (iconName: string) => {
    const iconProps = { className: "h-4 w-4 text-muted-foreground" };
    switch (iconName) {
      case "Package":
        return <Package {...iconProps} />;
      case "CheckCircle":
        return <CheckCircle {...iconProps} />;
      case "Clock":
        return <Clock {...iconProps} />;
      case "TrendingUp":
        return <TrendingUp {...iconProps} />;
      case "XCircle":
        return <XCircle {...iconProps} />;
      default:
        return <Package {...iconProps} />;
    }
  };

  const getChangeIcon = (changeType?: string) => {
    const iconProps = { className: "h-3 w-3" };
    switch (changeType) {
      case "increase":
        return <TrendingUp {...iconProps} className="h-3 w-3 text-green-500" />;
      case "decrease":
        return <TrendingDown {...iconProps} className="h-3 w-3 text-red-500" />;
      case "neutral":
        return <Minus {...iconProps} className="h-3 w-3 text-gray-500" />;
      default:
        return null;
    }
  };

  const getChangeColor = (changeType?: string) => {
    switch (changeType) {
      case "increase":
        return "text-green-600";
      case "decrease":
        return "text-red-600";
      case "neutral":
        return "text-gray-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statsCards.map((card, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {card.title}
            </CardTitle>
            {getIcon(card.icon || "Package")}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{card.value.toLocaleString()}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
              {card.change !== undefined && (
                <div className={`flex items-center space-x-1 ${getChangeColor(card.changeType)}`}>
                  {getChangeIcon(card.changeType)}
                  <span>{card.change.toFixed(1)}%</span>
                </div>
              )}
              <span className="text-muted-foreground">{card.description}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
