import React, { useState } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import type { DateRange } from "react-day-picker";

interface DateRangePickerProps {
  onDateRangeChange: (startDate?: number, endDate?: number) => void;
  className?: string;
}

export default function DateRangePicker({ onDateRangeChange, className }: DateRangePickerProps) {
  const { t } = useTranslation();
  const [date, setDate] = useState<DateRange | undefined>();
  const [quickSelect, setQuickSelect] = useState<string>("7d");

  // 预设时间范围选项（国际化）
  const quickOptions = [
    { value: "7d", label: t('dashboard.dateRangePicker.quickOptions.7d') },
    { value: "30d", label: t('dashboard.dateRangePicker.quickOptions.30d') },
    { value: "90d", label: t('dashboard.dateRangePicker.quickOptions.90d') },
    { value: "custom", label: t('dashboard.dateRangePicker.quickOptions.custom') },
  ];

  // 处理快速选择
  const handleQuickSelect = (value: string) => {
    console.log('DateRangePicker handleQuickSelect called:', value);
    setQuickSelect(value);

    if (value === "custom") {
      // 自定义范围，不自动设置日期
      return;
    }

    const now = new Date();
    const days = parseInt(value.replace('d', ''));
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

    const newDateRange = {
      from: startDate,
      to: now,
    };

    setDate(newDateRange);
    console.log('DateRangePicker calling onDateRangeChange:', { startDate: startDate.getTime(), endDate: now.getTime() });
    onDateRangeChange(startDate.getTime(), now.getTime());
  };

  // 处理日期范围选择
  const handleDateSelect = (selectedDate: DateRange | undefined) => {
    setDate(selectedDate);

    if (selectedDate?.from && selectedDate?.to) {
      // 设置时间为当天的开始和结束
      const startDate = new Date(selectedDate.from);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(selectedDate.to);
      endDate.setHours(23, 59, 59, 999);

      onDateRangeChange(startDate.getTime(), endDate.getTime());
    } else if (selectedDate?.from) {
      // 只选择了开始日期
      const startDate = new Date(selectedDate.from);
      startDate.setHours(0, 0, 0, 0);
      onDateRangeChange(startDate.getTime(), undefined);
    } else {
      // 清空选择
      onDateRangeChange(undefined, undefined);
    }
  };

  // 初始化默认选择（最近7天）- 立即执行而不是异步
  React.useEffect(() => {
    console.log('DateRangePicker useEffect initializing...');
    handleQuickSelect("7d");
  }, []);

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      {/* 快速选择下拉框 */}
      <Select value={quickSelect} onValueChange={handleQuickSelect}>
        <SelectTrigger className="w-[160px]">
          <SelectValue placeholder={t('dashboard.dateRangePicker.placeholder')} />
        </SelectTrigger>
        <SelectContent>
          {quickOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* 自定义日期范围选择器 */}
      {quickSelect === "custom" && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              id="date"
              variant="outline"
              className={cn(
                "w-[280px] justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, "yyyy-MM-dd")} -{" "}
                    {format(date.to, "yyyy-MM-dd")}
                  </>
                ) : (
                  format(date.from, "yyyy-MM-dd")
                )
              ) : (
                <span>{t('dashboard.dateRangePicker.customDatePlaceholder')}</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={handleDateSelect}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
      )}

      {/* 显示当前选择的日期范围（非自定义模式） */}
      {quickSelect !== "custom" && date?.from && date?.to && (
        <div className="text-sm text-muted-foreground">
          {t('dashboard.dateRangePicker.dateRangeDisplay', {
            from: format(date.from, "MM-dd"),
            to: format(date.to, "MM-dd")
          })}
        </div>
      )}
    </div>
  );
}
