import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, type ChartConfig } from "@/components/ui/chart";
import { Bar, Bar<PERSON>hart, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Line, LineChart } from "recharts";
import { Info } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { DashboardOrderTrendResponse } from "@/api/dashboard/dashboard-model";

interface OrderTrendChartProps {
  data: DashboardOrderTrendResponse;
  loading?: boolean;
  chartType?: 'bar' | 'line';
}

// 将 chartConfig 移到组件内部以支持国际化

export default function OrderTrendChart({
  data,
  loading,
  chartType = 'bar'
}: OrderTrendChartProps) {
  const { t } = useTranslation();

  // 国际化的图表配置
  const chartConfig = {
    orderCount: {
      label: t('dashboard.charts.orderTrend.labels.totalOrders'),
      color: "#3b82f6", // 蓝色
    },
    completedCount: {
      label: t('dashboard.charts.orderTrend.labels.completedOrders'),
      color: "#10b981", // 绿色
    },
    processingCount: {
      label: t('dashboard.charts.orderTrend.labels.processingOrders'),
      color: "#f59e0b", // 橙色
    },
    cancelledCount: {
      label: t('dashboard.charts.orderTrend.labels.cancelledOrders'),
      color: "#ef4444", // 红色
    },
  } satisfies ChartConfig;
  if (loading) {
    return (
      <Card className="col-span-4">
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] bg-gray-100 rounded animate-pulse"></div>
        </CardContent>
      </Card>
    );
  }

  if (!data.dataPoints || data.dataPoints.length === 0) {
    return (
      <Card className="col-span-4">
        <CardHeader>
          <CardTitle>{t('dashboard.charts.orderTrend.title')}</CardTitle>
          <CardDescription>{t('dashboard.charts.orderTrend.description', { totalOrders: 0, averageOrders: '0.0' })}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col justify-center items-center h-[400px] text-muted-foreground">
            <Info className="w-8 h-8 mb-2" />
            <p className="text-sm">{t('dashboard.charts.orderTrend.noData')}</p>
            <p className="text-xs text-muted-foreground mt-1">{t('dashboard.charts.orderTrend.noDataHint')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 格式化数据用于图表显示
  const chartData = data.dataPoints.map(point => ({
    ...point,
    displayDate: formatDateForDisplay(point.date),
    // 确保数值类型正确
    orderCount: typeof point.orderCount === 'string' ? parseInt(point.orderCount, 10) : point.orderCount,
    completedCount: typeof point.completedCount === 'string' ? parseInt(point.completedCount, 10) : point.completedCount,
    processingCount: typeof point.processingCount === 'string' ? parseInt(point.processingCount, 10) : point.processingCount,
    cancelledCount: typeof point.cancelledCount === 'string' ? parseInt(point.cancelledCount, 10) : point.cancelledCount,
  }));

  // 计算汇总信息
  const { summary } = data;

  // 计算Y轴的合理范围
  const orderCounts = chartData.map(item => item.orderCount).filter(count => typeof count === 'number' && !isNaN(count));
  const maxValue = orderCounts.length > 0 ? Math.max(...orderCounts) : 100;
  const minValue = orderCounts.length > 0 ? Math.min(...orderCounts) : 0;

  // 处理边缘情况：如果所有值相同，添加适当的范围
  const range = maxValue - minValue;
  const padding = range > 0 ? range * 0.1 : maxValue * 0.2; // 10%的上下边距，或者20%如果所有值相同

  const yAxisDomain = [
    Math.max(0, Math.floor(minValue - padding)), // 确保最小值不小于0
    Math.ceil(maxValue + padding)
  ];

  // 添加调试日志
  console.log('OrderTrendChart Debug:', {
    originalDataPoints: data.dataPoints,
    chartData: chartData,
    maxValue,
    minValue,
    yAxisDomain,
    peakDayValue: summary.peakDay?.orderCount
  });

  return (
    <Card className="col-span-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle>{t('dashboard.charts.orderTrend.title')}</CardTitle>
            <CardDescription>
              {t('dashboard.charts.orderTrend.description', {
                totalOrders: summary.totalOrders,
                averageOrders: summary.averageOrdersPerDay.toFixed(1)
              })}
              {summary.peakDay && (
                <span className="ml-2">
                  · {t('dashboard.charts.orderTrend.peakDay', {
                    count: summary.peakDay.orderCount,
                    date: formatDateForDisplay(summary.peakDay.date)
                  })}
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            {summary.growthRate !== 0 && (
              <div className={`flex items-center space-x-1 ${summary.growthRate > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                <span>{summary.growthRate > 0 ? '↗' : '↘'}</span>
                <span>{Math.abs(summary.growthRate).toFixed(1)}%</span>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[400px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'bar' ? (
              <BarChart data={chartData} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="displayDate"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  interval="preserveStartEnd"
                  fontSize={12}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  fontSize={12}
                  width={60}
                  domain={yAxisDomain}
                  type="number"
                  allowDataOverflow={false}
                  scale="linear"
                />
                <Bar
                  dataKey="orderCount"
                  fill={chartConfig.orderCount.color}
                  radius={[4, 4, 0, 0]}
                />
                <ChartTooltip
                  cursor={false}
                  content={({ active, payload, label }) => {
                    if (!active || !payload || payload.length === 0) return null;

                    const data = payload[0].payload;
                    return (
                      <div className="bg-background/95 backdrop-blur-sm border border-border/50 shadow-lg rounded-lg p-3 min-w-[200px]">
                        <div className="font-medium text-sm mb-2">{label}</div>
                        <div className="space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">{t('dashboard.charts.orderTrend.labels.totalOrders')}：</span>
                            <span className="font-medium">{data.orderCount}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">{t('dashboard.charts.orderTrend.labels.completedOrders')}：</span>
                            <span className="font-medium text-green-600">{data.completedCount}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">{t('dashboard.charts.orderTrend.labels.processingOrders')}：</span>
                            <span className="font-medium text-blue-600">{data.processingCount}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">{t('dashboard.charts.orderTrend.labels.cancelledOrders')}：</span>
                            <span className="font-medium text-red-600">{data.cancelledCount}</span>
                          </div>
                        </div>
                      </div>
                    );
                  }}
                />
              </BarChart>
            ) : (
              <LineChart data={chartData} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="displayDate"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  interval="preserveStartEnd"
                  fontSize={12}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  fontSize={12}
                  width={60}
                  domain={yAxisDomain}
                  type="number"
                  allowDataOverflow={false}
                  scale="linear"
                />
                <Line
                  type="monotone"
                  dataKey="orderCount"
                  stroke={chartConfig.orderCount.color}
                  strokeWidth={2}
                  dot={{ r: 4, fill: chartConfig.orderCount.color }}
                  activeDot={{ r: 6, fill: chartConfig.orderCount.color, stroke: '#fff', strokeWidth: 2 }}
                />
                <ChartTooltip
                  cursor={false}
                  content={({ active, payload, label }) => {
                    if (!active || !payload || payload.length === 0) return null;

                    const data = payload[0].payload;
                    return (
                      <div className="bg-background/95 backdrop-blur-sm border border-border/50 shadow-lg rounded-lg p-3 min-w-[200px]">
                        <div className="font-medium text-sm mb-2">{label}</div>
                        <div className="space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">{t('dashboard.charts.orderTrend.labels.totalOrders')}：</span>
                            <span className="font-medium">{data.orderCount}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">{t('dashboard.charts.orderTrend.labels.completedOrders')}：</span>
                            <span className="font-medium text-green-600">{data.completedCount}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">{t('dashboard.charts.orderTrend.labels.processingOrders')}：</span>
                            <span className="font-medium text-blue-600">{data.processingCount}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">{t('dashboard.charts.orderTrend.labels.cancelledOrders')}：</span>
                            <span className="font-medium text-red-600">{data.cancelledCount}</span>
                          </div>
                        </div>
                      </div>
                    );
                  }}
                />
              </LineChart>
            )}
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

/**
 * 格式化日期用于显示
 */
function formatDateForDisplay(dateString: string): string {
  const date = new Date(dateString);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${month}-${day}`;
}
