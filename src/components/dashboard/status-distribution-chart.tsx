import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, type ChartConfig } from "@/components/ui/chart";
import { Pie<PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend } from "recharts";
import { Info } from "lucide-react";
import { useTranslation } from "react-i18next";

interface StatusDistributionChartProps {
  data: Record<string, number>;
  loading?: boolean;
}

const statusConfig = {
  COMPLETED: {
    label: "已完成",
    color: "hsl(142 76% 36%)", // 绿色
  },
  CREATED: {
    label: "已创建",
    color: "hsl(213 94% 68%)", // 蓝色
  },
  PROCESSING: {
    label: "处理中",
    color: "hsl(45 93% 47%)", // 橙色
  },
  SUPPLIER_MATCHED: {
    label: "供应商匹配",
    color: "hsl(45 93% 47%)", // 橙色
  },
  FAILED: {
    label: "失败",
    color: "hsl(0 84% 60%)", // 红色
  },
  CANCELLED: {
    label: "已取消",
    color: "hsl(0 0% 45%)", // 灰色
  },
  SPLIT: {
    label: "已拆分",
    color: "hsl(262 83% 58%)", // 紫色
  },
  // 轨迹相关状态
  TRACKING_NOT_FOUND: {
    label: "轨迹未找到",
    color: "hsl(0 0% 60%)",
  },
  TRACKING_PRE_ADVICE_RECEIVED: {
    label: "预通知已收到",
    color: "hsl(200 100% 70%)",
  },
  TRACKING_PICKED_UP: {
    label: "已取件",
    color: "hsl(180 100% 40%)",
  },
  TRACKING_IN_TRANSIT: {
    label: "运输中",
    color: "hsl(220 100% 50%)",
  },
  TRACKING_ARRIVED_DESTINATION_COUNTRY: {
    label: "已到达目的国",
    color: "hsl(240 100% 60%)",
  },
  TRACKING_IN_CUSTOMS: {
    label: "海关处理中",
    color: "hsl(260 100% 60%)",
  },
  TRACKING_CUSTOMS_CLEARED: {
    label: "清关完成",
    color: "hsl(280 100% 60%)",
  },
  TRACKING_ARRIVED_FOR_PICKUP: {
    label: "到达待取",
    color: "hsl(300 100% 60%)",
  },
  TRACKING_OUT_FOR_DELIVERY: {
    label: "派送中",
    color: "hsl(320 100% 60%)",
  },
  TRACKING_DELIVERY_FAILED: {
    label: "派送失败",
    color: "hsl(15 100% 50%)",
  },
  TRACKING_DELIVERED: {
    label: "已送达",
    color: "hsl(120 100% 35%)",
  },
  TRACKING_RETURNED: {
    label: "已退回",
    color: "hsl(30 100% 45%)",
  },
  TRACKING_CANCELLED: {
    label: "已取消",
    color: "hsl(0 0% 50%)",
  },
  TRACKING_UNKNOWN: {
    label: "状态未知",
    color: "hsl(0 0% 65%)",
  },
  // 合并后的显示状态
  TRACKING_PENDING: {
    label: "轨迹待处理",
    color: "hsl(0 0% 60%)",
  },
  TRACKING_EXCEPTION: {
    label: "轨迹异常",
    color: "hsl(0 100% 45%)",
  },
} satisfies ChartConfig;

export default function StatusDistributionChart({ data, loading }: StatusDistributionChartProps) {
  const { t } = useTranslation();

  // 获取状态的显示名称（国际化）
  const getStatusDisplayName = (status: string): string => {
    return t(`dashboard.charts.statusDistribution.statuses.${status}`, { defaultValue: status });
  };

  // 获取状态的颜色
  const getStatusColor = (status: string): string => {
    return statusConfig[status as keyof typeof statusConfig]?.color || "hsl(var(--muted))";
  };
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] bg-gray-100 rounded animate-pulse"></div>
        </CardContent>
      </Card>
    );
  }

  // 转换数据格式
  const chartData = Object.entries(data)
    .filter(([_, count]) => count > 0)
    .map(([status, count]) => ({
      status,
      count: typeof count === 'string' ? parseInt(count, 10) : count,
      displayName: getStatusDisplayName(status),
      color: getStatusColor(status),
    }))
    .sort((a, b) => b.count - a.count);

  if (chartData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('dashboard.charts.statusDistribution.title')}</CardTitle>
          <CardDescription>{t('dashboard.charts.statusDistribution.description', { total: 0 })}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col justify-center items-center h-[400px] text-muted-foreground">
            <Info className="w-8 h-8 mb-2" />
            <p className="text-sm">{t('dashboard.charts.statusDistribution.noData')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const total = chartData.reduce((sum, item) => sum + item.count, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('dashboard.charts.statusDistribution.title')}</CardTitle>
        <CardDescription>
          {t('dashboard.charts.statusDistribution.description', { total })}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={statusConfig} className="h-[400px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="count"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <ChartTooltip
                content={({ active, payload }) => {
                  if (!active || !payload || payload.length === 0) return null;

                  const data = payload[0].payload;
                  const percentage = ((data.count / total) * 100).toFixed(1);

                  return (
                    <div className="bg-background/95 backdrop-blur-sm border border-border/50 shadow-lg rounded-lg p-3">
                      <div className="font-medium text-sm mb-1">{data.displayName}</div>
                      <div className="text-xs space-y-1">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">{t('dashboard.charts.statusDistribution.tooltip.count')}：</span>
                          <span className="font-medium">{data.count}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">{t('dashboard.charts.statusDistribution.tooltip.percentage')}：</span>
                          <span className="font-medium">{percentage}%</span>
                        </div>
                      </div>
                    </div>
                  );
                }}
              />
              <Legend
                content={({ payload }) => (
                  <div className="flex flex-wrap justify-center gap-4 mt-4">
                    {payload?.map((entry, index) => {
                      const data = chartData[index];
                      const percentage = ((data.count / total) * 100).toFixed(1);
                      return (
                        <div key={index} className="flex items-center space-x-2 text-xs">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: entry.color }}
                          />
                          <span className="text-muted-foreground">
                            {data.displayName}: {data.count} ({percentage}%)
                          </span>
                        </div>
                      );
                    })}
                  </div>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
