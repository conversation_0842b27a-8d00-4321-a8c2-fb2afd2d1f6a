import { Dialog, DialogContent } from '@/components/ui/new/dialog';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { createResponsiveWrapper } from 'pushmodal';

export const { Wrapper, Content } = createResponsiveWrapper({
    desktop: {
        Wrapper: Dialog,
        Content: DialogContent,
    },
    mobile: {
        Wrapper: Drawer,
        Content: DrawerContent,
    },
    breakpoint: 640,
});