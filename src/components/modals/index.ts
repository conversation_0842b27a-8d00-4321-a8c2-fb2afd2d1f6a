import { createPushModal } from 'pushmodal'
import { Wrapper } from './dynamic'
import { DoubleCheckModal } from './double-check-modal'


/**
 * https://github.com/lindesvard/pushmodal
 */

export const {
    pushModal,
    popModal,
    popAllModals,
    replaceWithModal,
    useOnPushModal,
    onPushModal,
    ModalProvider
} = createPushModal({
    modals: {
        "DoubleCheckModal": {
            Component: DoubleCheckModal,
            Wrapper: Wrapper,
        },
    },
})
