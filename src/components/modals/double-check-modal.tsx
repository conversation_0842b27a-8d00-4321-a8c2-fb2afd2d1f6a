import { useState } from "react"
import CommonButton from "../ui/button/new-button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "../ui/dialog"


type DoubleCheckModalProps = {
    title: string
    description: string
    onConfirm: () => void
}


export const DoubleCheckModal = ({ title, description, onConfirm }: DoubleCheckModalProps) => {

    const [isOpen, setIsOpen] = useState(true)

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="" aria-describedby={undefined}>
                <DialogHeader>
                    <DialogTitle>{title}</DialogTitle>
                    <DialogDescription>{description}</DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <CommonButton variant="outline" onClick={() => setIsOpen(false)}>取消</CommonButton>
                    <CommonButton onClick={
                        () => {
                            onConfirm()
                            setIsOpen(false)
                        }
                    }>确认</CommonButton>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}