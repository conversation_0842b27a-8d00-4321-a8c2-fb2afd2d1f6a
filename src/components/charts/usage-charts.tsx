// "use client"

// import React, { useEffect, useState } from "react"

// import { Bar, BarChart, XAxis, ResponsiveContainer, YAxis, CartesianGrid } from "recharts"

// import { type ChartConfig, ChartContainer, ChartTooltip } from "@/components/ui/chart"
// import { dashStatisticApi } from "@/api/dash/dash-api"
// import type { OrganizationDailyBillResponse } from "@/api/dash/dash-model"
// import { useAtomValue } from "jotai"
// import { Loading } from "../ui/loading"
// import { Info } from "lucide-react"

// const initialChartConfig = {
//     color1: {
//         label: "颜色1",
//         color: "oklch(0.398 0.07 227.392)",
//     },
//     color2: {
//         label: "颜色2",
//         color: "oklch(0.828 0.189 84.429)",
//     },
//     color3: {
//         label: "颜色3",
//         color: "oklch(0.769 0.188 70.08)",
//     },
//     color4: {
//         label: "颜色4",
//         color: "oklch(0.488 0.243 264.376)",
//     },
//     color5: {
//         label: "颜色5",
//         color: "oklch(0.696 0.17 162.48)",
//     },
//     color6: {
//         label: "颜色6",
//         color: "oklch(0.627 0.265 303.9)",
//     },
//     color7: {
//         label: "颜色7",
//         color: "oklch(0.645 0.246 16.439)",
//     },
//     color8: {
//         label: "颜色8",
//         color: "oklch(0.577 0.245 27.325)",
//     },
//     color9: {
//         label: "颜色9",
//         color: "oklch(0.704 0.191 22.216)",
//     },
//     color10: {
//         label: "颜色10",
//         color: "oklch(0.725 0.255 264.5)",
//     },
// } satisfies ChartConfig

// interface TransformedData {
//     date: string;
//     [modelName: string]: number | string;
// }

// const colors = [
//     "oklch(0.398 0.07 227.392)",
//     "oklch(0.828 0.189 84.429)",
//     "oklch(0.769 0.188 70.08)",
//     "oklch(0.488 0.243 264.376)",
//     "oklch(0.696 0.17 162.48)",
//     "oklch(0.627 0.265 303.9)",
//     "oklch(0.645 0.246 16.439)",
//     "oklch(0.577 0.245 27.325)",
//     "oklch(0.704 0.191 22.216)",
//     "oklch(0.725 0.255 264.5)",
// ];

// interface UsageChartsProps {
//     selectedYear: number;
//     selectedMonth: number;
// }

// export default function UsageCharts({ selectedYear, selectedMonth }: UsageChartsProps) {
//     const [chartData, setChartData] = useState<TransformedData[]>([]);
//     const [chartConfig, setChartConfig] = useState<ChartConfig>(initialChartConfig);
//     const [modelKeys, setModelKeys] = useState<string[]>([]);
//     const [loading, setLoading] = useState(true);
//     const [error, setError] = useState<string | null>(null);

//     const organization = useAtomValue(organizationState)

//     useEffect(() => {
//         const orgId = organization?.organizationId;

//         if (!orgId) {
//             setError("机构 ID 不可用，无法加载图表数据。");
//             setLoading(false);
//             setChartData([]);
//             return;
//         }

//         if (!selectedYear || !selectedMonth) {
//             setError("年份或月份无效，无法加载图表数据。");
//             setLoading(false);
//             setChartData([]);
//             return;
//         }

//         const fetchData = async () => {
//             try {
//                 setLoading(true);
//                 setError(null);
//                 const response = await dashStatisticApi.getDashStatistic({
//                     selectedYear,
//                     selectedMonth,
//                     organizationId: orgId,
//                 });

//                 const transformedData: TransformedData[] = [];
//                 const newModelKeys = new Set<string>();
//                 const newChartConfig: ChartConfig = { ...initialChartConfig };
//                 let colorIndex = 0;

//                 Object.keys(response).forEach(dateTimestampKey => {
//                     const dailyData: TransformedData = { date: dateTimestampKey };
//                     response[dateTimestampKey].forEach((bill: OrganizationDailyBillResponse) => {
//                         dailyData[bill.modelName] = bill.quota;
//                         if (!newModelKeys.has(bill.modelName)) {
//                             newModelKeys.add(bill.modelName);
//                             if (!newChartConfig[bill.modelName]) {
//                                 newChartConfig[bill.modelName] = {
//                                     label: bill.modelName,
//                                     color: colors[colorIndex % colors.length],
//                                 };
//                                 colorIndex++;
//                             }
//                         }
//                     });
//                     transformedData.push(dailyData);
//                 });

//                 transformedData.sort((a, b) => Number(a.date) - Number(b.date));

//                 const dataForTicks = transformedData;
//                 let newXAxisTicks: string[] = [];
//                 if (dataForTicks.length > 0) {
//                     const maxTicks = 10;
//                     if (dataForTicks.length <= maxTicks) {
//                         newXAxisTicks = dataForTicks.map(d => d.date);
//                     } else {
//                         newXAxisTicks.push(dataForTicks[0].date);
//                         const step = (dataForTicks.length - 1) / (maxTicks - 1);
//                         for (let i = 1; i < maxTicks - 1; i++) {
//                             const tickIndex = Math.round(i * step);
//                             if (tickIndex > 0 && tickIndex < dataForTicks.length - 1) {
//                                 newXAxisTicks.push(dataForTicks[tickIndex].date);
//                             }
//                         }
//                         newXAxisTicks.push(dataForTicks[dataForTicks.length - 1].date);

//                         newXAxisTicks = Array.from(new Set(newXAxisTicks)).sort((a, b) => Number(a) - Number(b));
//                     }
//                 }

//                 setChartData(transformedData);
//                 setModelKeys(Array.from(newModelKeys));
//                 setChartConfig(newChartConfig);

//             } catch (err) {
//                 setError(err instanceof Error ? err.message : "加载图表数据时发生未知错误");
//                 console.error("Failed to fetch chart data:", err);
//                 setChartData([]);
//             } finally {
//                 setLoading(false);
//             }
//         };

//         fetchData();
//     }, [organization, selectedYear, selectedMonth]);

//     if (loading) {
//         return <Loading />;
//     }

//     if (error) {
//         return <div className="flex justify-center items-center h-full text-red-500">错误: {error}</div>;
//     }

//     if (chartData.length === 0) {
//         return (
//             <div className="flex flex-col justify-center items-center h-full text-zinc-500 text-sm gap-2">
//                 <Info className="w-4 h-4" />
//                 <span className="text-black text-xs">暂无数据</span>
//                 <span className="text-zinc-500 text-xs tracking-wider">请检查是否已开通模型</span>
//             </div>
//         );
//     }

//     return (
//         <div className="h-full w-full flex flex-col p-2">
//             <div className="w-full h-[600px] mt-12 flex justify-center px-8">
//                 <ChartContainer className="h-full w-full" config={chartConfig}>
//                     <ResponsiveContainer width="100%" height="100%">
//                         <BarChart data={chartData} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
//                             <CartesianGrid strokeDasharray="3 3" />
//                             <XAxis
//                                 dataKey="date"
//                                 tickLine={false}
//                                 tickMargin={10}
//                                 axisLine={false}
//                                 interval={2}
//                                 tickFormatter={(value) => {
//                                     const numericTimestamp = Number(value);
//                                     if (isNaN(numericTimestamp)) {
//                                         return String(value);
//                                     }
//                                     const dateObj = new Date(numericTimestamp);
//                                     if (isNaN(dateObj.getTime())) {
//                                         return String(value);
//                                     }
//                                     const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
//                                     const day = dateObj.getDate().toString().padStart(2, '0');
//                                     return `${month}-${day}`;
//                                 }}
//                             />
//                             <YAxis
//                                 tickLine={false}
//                                 axisLine={false}
//                                 stroke="#64748b"
//                                 fontSize={12}
//                                 tickFormatter={(value) => {
//                                     // 根据数值大小动态调整显示格式
//                                     if (value >= 1000) {
//                                         return `${(value / 1000).toFixed(1)}k`;
//                                     } else if (value >= 1) {
//                                         return `${value.toFixed(2)}`;
//                                     } else {
//                                         return `${value.toFixed(4)}`;
//                                     }
//                                 }}
//                                 width={80}
//                             />
//                             {modelKeys.map((modelKey, index) => (
//                                 <Bar
//                                     key={modelKey}
//                                     dataKey={modelKey}
//                                     stackId="a"
//                                     fill={chartConfig[modelKey]?.color || colors[index % colors.length]}
//                                     radius={
//                                         modelKeys.length === 1 ? [4, 4, 0, 0] :
//                                             index === 0 ? [0, 0, 4, 4] :
//                                                 index === modelKeys.length - 1 ? [4, 4, 0, 0] :
//                                                     [0, 0, 0, 0]
//                                     }
//                                 />
//                             ))}
//                             <ChartTooltip
//                                 content={({ active, payload, label }) => {
//                                     if (!active || !payload || payload.length === 0) return null;

//                                     // 过滤出有效的数据并按值倒序排序
//                                     const validData = payload
//                                         .filter(item => item.value && Number(item.value) > 0)
//                                         .sort((a, b) => Number(b.value) - Number(a.value));

//                                     if (validData.length === 0) return null; // Prevent empty tooltip

//                                     // 计算总量
//                                     const total = validData.reduce((sum, item) => sum + Number(item.value || 0), 0);
//                                     const chartType = 'amount'; // Assuming chartType is 'amount'

//                                     return (
//                                         <div className="w-[300px] bg-background/95 backdrop-blur-sm border border-border/50 shadow-lg rounded-lg p-3">
//                                             {/* 日期标题 */}
//                                             <div className="mb-2 flex basis-full items-center border-b border-border/50 pb-2 text-xs font-medium text-foreground">
//                                                 日期
//                                                 <div className="ml-auto flex items-baseline gap-0.5 font-mono font-medium tabular-nums text-foreground">
//                                                     {(() => {
//                                                         const numericTimestamp = Number(label);
//                                                         if (isNaN(numericTimestamp)) {
//                                                             return String(label); // Fallback if not a valid number
//                                                         }
//                                                         const dateObj = new Date(numericTimestamp);
//                                                         if (isNaN(dateObj.getTime())) {
//                                                             return String(label); // Fallback if not a valid date
//                                                         }
//                                                         const year = dateObj.getFullYear();
//                                                         const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
//                                                         const day = dateObj.getDate().toString().padStart(2, '0');
//                                                         return `${year}-${month}-${day}`;
//                                                     })()}
//                                                 </div>
//                                             </div>

//                                             {/* 排序后的模型数据 */}
//                                             {validData.map((item, index) => (
//                                                 <div key={`${item.dataKey}-${index}`} className="flex items-center gap-2 py-1">
//                                                     <div
//                                                         className="h-2.5 w-2.5 shrink-0 rounded-[2px]"
//                                                         style={{
//                                                             backgroundColor: item.color,
//                                                         } as React.CSSProperties}
//                                                     />
//                                                     <span className="flex-1 text-xs text-foreground">
//                                                         {chartConfig[item.dataKey as keyof typeof chartConfig]?.label || item.dataKey}
//                                                     </span>
//                                                     <div className="ml-auto flex items-baseline gap-0.5 font-mono font-medium tabular-nums text-foreground text-xs">
//                                                         ${chartType === 'amount'
//                                                             ? `${Number(item.value).toFixed(4)}`
//                                                             : Number(item.value).toLocaleString()
//                                                         }
//                                                     </div>
//                                                 </div>
//                                             ))}

//                                             {/* 总量 */}
//                                             {validData.length > 0 && (
//                                                 <div className="mt-2 flex basis-full items-center border-t border-border/50 pt-2 text-xs font-medium text-foreground">
//                                                     <span className="font-semibold">
//                                                         总{chartType === 'amount' ? '金额' : 'Tokens'}
//                                                     </span>
//                                                     <div className="ml-auto flex items-baseline gap-0.5 font-mono font-medium tabular-nums text-foreground">
//                                                         ${chartType === 'amount'
//                                                             ? `${total.toFixed(4)}`
//                                                             : total.toLocaleString()
//                                                         }
//                                                     </div>
//                                                 </div>
//                                             )}
//                                         </div>
//                                     );
//                                 }}
//                                 cursor={false}
//                             />
//                         </BarChart>
//                     </ResponsiveContainer>
//                 </ChartContainer>
//             </div>
//         </div>
//     )
// }
