import { Check } from "lucide-react"

interface StatusIconProps {
    variant?: "completed" | "progress-gray" | "progress-blue" | "pending"
    size?: "sm" | "md" | "lg"
    className?: string
}

export default function StatusIcon({ variant = "completed", size = "md", className = "" }: StatusIconProps) {
    const sizeClasses = {
        sm: "w-4 h-4",
        md: "w-8 h-8",
        lg: "w-12 h-12",
    }

    const sizes = {
        sm: 24,
        md: 32,
        lg: 48,
    }

    const iconSizes = {
        sm: "w-3 h-3",
        md: "w-4 h-4",
        lg: "w-6 h-6",
    }

    const currentSize = sizes[size]

    const renderVariant = () => {
        switch (variant) {
            case "completed":
                return (
                    <div className={`${sizeClasses[size]} bg-gray-600 rounded-full flex items-center justify-center`}>
                        <Check className={`${iconSizes[size]} text-white stroke-2`} />
                    </div>
                )

            case "progress-gray":
                return (
                    <svg width={currentSize} height={currentSize} viewBox="0 0 32 32" className={sizeClasses[size]}>
                        <circle cx="16" cy="16" r="14" fill="none" stroke="#6b7280" strokeWidth="2" />
                        <path d="M 16 4 A 12 12 0 0 1 16 28 Z" fill="#6b7280" />
                    </svg>
                )

            case "progress-blue":
                return (
                    <svg width={currentSize} height={currentSize} viewBox="0 0 32 32" className={sizeClasses[size]}>
                        <circle cx="16" cy="16" r="14" fill="none" stroke="#3b82f6" strokeWidth="2" />
                        <path d="M 16 4 A 12 12 0 0 1 16 28 Z" fill="#3b82f6" />
                    </svg>
                )

            case "pending":
                return (
                    <svg width={currentSize} height={currentSize} viewBox="0 0 32 32" className={sizeClasses[size]}>
                        <circle cx="16" cy="16" r="14" fill="none" stroke="#9ca3af" strokeWidth="2" strokeDasharray="4 4" />
                    </svg>
                )

            default:
                return null
        }
    }

    return <div className={`inline-flex items-center justify-center ${className}`}>{renderVariant()}</div>
}
