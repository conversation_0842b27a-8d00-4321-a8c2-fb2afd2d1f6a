import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { Hash, Package2, User } from "lucide-react"
import useSearchParamsManager from "@/hooks/use-url-param"
import { useEffect, useState } from "react"

export interface SelectOption {
  label: string
  value: string
  icon?: React.ReactNode
}

interface SelectWithInputProps {
  options?: SelectOption[]
  selectPlaceholder?: string
  inputPlaceholder?: string
  className?: string
}

export function SelectWithInput({
  options = [
    { label: "订单号", value: "orderNo", icon: <Hash className="mr-2 h-3.5 w-3.5" /> },
    { label: "商品名称", value: "productName", icon: <Package2 className="mr-2 h-3.5 w-3.5" /> },
    { label: "收货人", value: "receiverName", icon: <User className="mr-2 h-3.5 w-3.5" /> }
  ],
  inputPlaceholder = "请输入...",
  className,
}: SelectWithInputProps) {
  const { getParam, addParam, deleteParam } = useSearchParamsManager();
  const [selectedType, setSelectedType] = useState(options[0].value);
  const [inputValue, setInputValue] = useState('');
  
  // 初始化时从 URL 读取参数
  useEffect(() => {
    const type = options.find(opt => getParam(opt.value))?.value || options[0].value;
    setSelectedType(type);
    setInputValue(getParam(type) || '');
  }, []);

  const handleSelectChange = (value: string) => {
    // 清除之前的搜索参数
    if (selectedType) {
      deleteParam(selectedType);
    }
    setSelectedType(value);
    setInputValue('');
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
    if (selectedType) {
      if (value) {
        addParam(selectedType, value);
      } else {
        deleteParam(selectedType);
      }
    }
  };

  return (
    <div className={`flex items-center ${className}`}>
      <Select value={selectedType} onValueChange={handleSelectChange}>
        <SelectTrigger 
          className={cn(
            "w-[130px] rounded-r-none border-r-0 [&>span]:truncate h-8 text-xs",
            "focus:ring-0 focus:ring-offset-0",
            "[&[data-state=open]]:border-r-0",
            "[&[data-placeholder]]:text-muted-foreground"
          )}
        >
          <SelectValue>
            <div className="flex items-center">
              {options.find(opt => opt.value === selectedType)?.icon}
              {options.find(opt => opt.value === selectedType)?.label}
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value} 
              className="text-xs flex items-center"
            >
              <div className="flex items-center">
                {option.icon}
                {option.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Input
        className="w-[200px] rounded-l-none focus-visible:ring-offset-0 focus-visible:ring-0 h-8 text-xs"
        placeholder={inputPlaceholder}
        value={inputValue}
        onChange={(e) => handleInputChange(e.target.value)}
      />
    </div>
  )
} 