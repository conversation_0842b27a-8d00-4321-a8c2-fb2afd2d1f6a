import { Loader2 } from "lucide-react";

const CommonButton = ({
    children,
    variant = 'primary', // 默认为 'primary'
    size = 'md',
    className = '',
    loading = false,
    ...props
}: React.ComponentProps<"button"> & {
    loading?: boolean
    variant?: "secondary" | "outline" | "ghost" | "destructive" | "link" | "primary"
    size?: "xs" | "sm" | "md" | "lg"
}) => {
    // --- 基本的按钮类 ---
    // 这些类是大多数按钮变体共有的
    const baseButtonClasses = `
      relative group inline-flex w-fit items-center justify-center /* 移除了 gap-x-1.5, 因为它将由内部span处理 */
      rounded-md
      text-[0.8125rem] font-medium leading-5 cursor-pointer border
      transition-all duration-150 ease-in-out
      focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2
    `;

    // --- 特定变体的类 ---
    let variantClasses = '';
    let gradientOverlay = null; // 渐变覆盖层，主要用于 primary 和 destructive
    let contentGapClass = 'gap-x-1.5'; // 默认图标和文字的间距

    // --- 尺寸相关类 ---
    let sizeClasses = '';

    switch (size) {
        case 'xs':
            sizeClasses = `px-2 py-1 text-xs`;
            contentGapClass = 'gap-x-1';
            break;
        case 'sm':
            sizeClasses = `px-2.5 py-1.5 text-xs`;
            contentGapClass = 'gap-x-1.5';
            break;
        case 'md':
            sizeClasses = `px-3 py-1.5 text-sm`;
            break;
        case 'lg':
            sizeClasses = `px-4 py-2 text-sm`;
            break;
        default:
            sizeClasses = `px-3 py-1.5 text-sm`; // 默认为 md 尺寸
            break;
    }

    switch (variant) {
        case 'secondary':
            variantClasses = `
          bg-zinc-200 text-zinc-800 border-zinc-200
          hover:bg-zinc-300 active:bg-zinc-400
          focus-visible:ring-zinc-400
          shadow-sm
        `;
            break;
        case 'outline':
            variantClasses = `
          bg-transparent text-zinc-700 border-zinc-300  
          hover:bg-zinc-100  active:bg-zinc-200
          focus-visible:ring-zinc-500 shadow-xs
        `;
            break;
        case 'ghost':
            variantClasses = `
          bg-transparent text-zinc-700 border-transparent
          hover:bg-zinc-100 active:bg-zinc-200
          focus-visible:ring-zinc-500
        `;
            break;
        case 'destructive':
            variantClasses = `
          bg-red-600 text-white border-red-600
          hover:bg-red-700 active:bg-red-800
          focus-visible:ring-red-500
          shadow-[inset_0px_0.75px_0px_0px_hsla(0,0%,100%,.2),0px_1px_2px_0px_rgba(0,0,0,.2),0px_0px_0px_1px_#dc2626]
        `;
            // 破坏性按钮的渐变覆盖层
            gradientOverlay = (
                <span
                    className={`
              absolute inset-0 rounded-md transition-colors duration-150 ease-in-out
              bg-gradient-to-b from-white/[.10] to-transparent
              group-hover:from-white/[.15] group-active:from-white/[.05]
            `}
                ></span>
            );
            break;
        case 'link':
            variantClasses = `
          bg-transparent text-blue-600 border-transparent
          hover:underline hover:text-blue-700
          focus-visible:ring-blue-500
          /* 链接按钮样式不受size控制，有自己的特殊padding */
          ${size === 'xs' || size === 'sm' ? 'px-0.5 py-0.5' : 'px-1 py-0.5'}
        `;
            // 链接按钮使用更小的间距
            contentGapClass = size === 'xs' ? 'gap-x-0.5' : 'gap-x-1';
            // 链接按钮不使用标准的size类
            sizeClasses = '';
            break;
        case 'primary': // 默认情况也是 'primary'
        default:
            variantClasses = `
          bg-zinc-800 text-white border-zinc-800
          hover:bg-zinc-700 active:bg-zinc-600
          focus-visible:ring-zinc-300 focus-visible:ring-offset-zinc-900
          focus-visible:shadow-[inset_0px_0.75px_0px_0px_hsla(0,0%,100%,.2),0px_1px_2px_0px_rgba(0,0,0,.4),0px_0px_0px_1px_#18181b,0px_0px_0px_2px_#18181b,0px_0px_0px_4px_rgba(59,130,246,.3)]
          shadow-[inset_0px_0.75px_0px_0px_hsla(0,0%,100%,.2),0px_1px_2px_0px_rgba(0,0,0,.4),0px_0px_0px_1px_#18181b]
        `;
            // 主要按钮的渐变覆盖层
            gradientOverlay = (
                <span
                    className={`
              absolute inset-0 rounded-md transition-colors duration-150 ease-in-out
              bg-gradient-to-b from-white/[.10] to-transparent
              group-hover:from-white/[.15] group-active:from-white/[.05]
            `}
                ></span>
            );
            break;
    }

    return (
        <button
            disabled={loading}
            className={`
          ${baseButtonClasses}
          ${sizeClasses}
          ${variantClasses}
          ${className}
        `}
            {...props}
        >
            {loading && <Loader2 className="w-4 h-4 animate-spin" />}
            {gradientOverlay}
            {/* 文本内容和图标的容器: 相对定位在覆盖层之上, 使用inline-flex来水平排列子元素 (图标和文字) */}
            <span className={`relative z-10 inline-flex items-center ${contentGapClass}`}>
                {children}
            </span>
        </button>
    );
};

export default CommonButton;