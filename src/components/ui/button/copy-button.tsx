"use client"

import { useState, useEffect } from "react"
import { Co<PERSON> } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import * as clipboard from "clipboard-polyfill";
interface CopyButtonProps {
    text: string
    className?: string,
    size?: "xs" | "sm" | "md" | "lg"
}

export function CopyButton({ text, className, size = "sm" }: CopyButtonProps) {
    const [copied, setCopied] = useState(false)

    useEffect(() => {
        if (copied) {
            const timeout = setTimeout(() => {
                setCopied(false)
            }, 2000)
            return () => clearTimeout(timeout)
        }
    }, [copied])

    const handleCopy = async () => {
        try {
            await clipboard.writeText(text)
            setCopied(true)
        } catch (error) {
            console.error("Failed to copy text:", error)
        }
    }

    const sizeClass = {
        xs: "h-3 !w-3",
        sm: "h-4 !w-4",
        md: "h-5 !w-5",
        lg: "h-6 !w-6",
    }

    return (
        <button
            onClick={handleCopy}
            className={cn(
                `relative inline-flex items-center justify-center text-sm transition-colors hover:text-gray-700
                 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring
                  focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50
                  hover:cursor-pointer
                  ${sizeClass[size]}
                  `,
                className,
            )}
            aria-label={copied ? "Copied" : "Copy to clipboard"}
        >
            <AnimatePresence initial={false} mode="wait">
                {copied ? (
                    <motion.div
                        key="check"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="flex items-center justify-center"
                    >
                        <svg viewBox="0 0 24 24" width="16" height="16" fill="none" className="text-green-500">
                            <motion.path
                                d="M4 12L10 18L20 6"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                initial={{ pathLength: 0, pathOffset: 0 }}
                                animate={{ pathLength: 1, pathOffset: 0 }}
                                transition={{
                                    duration: 0.5,
                                    ease: "easeInOut",
                                }}
                            />
                        </svg>
                    </motion.div>
                ) : (
                    <motion.div
                        key="copy"
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.8, opacity: 0 }}
                        className="flex items-center justify-center"
                    >
                        <Copy className={cn(sizeClass[size])} />
                    </motion.div>
                )}
            </AnimatePresence>
        </button>
    )
}
