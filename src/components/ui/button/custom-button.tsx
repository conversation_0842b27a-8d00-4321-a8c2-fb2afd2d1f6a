import React from 'react';
import { cn } from '@/lib/utils';

export interface CustomButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    variant?: 'default' | 'outline' | 'ghost';
    size?: 'icon' | 'default'; // Added 'default' size for completeness, though not strictly used by month-picker for styling decisions beyond 'icon'
    children?: React.ReactNode;
}

const CustomButton: React.FC<CustomButtonProps> = ({
    variant = 'default',
    size,
    className,
    children,
    type = 'button', // Default type to "button"
    ...props
}) => {
    // Base styles, can be augmented by variant and className
    const baseStyles = "inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-300 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background";

    let variantSpecificStyles = "";
    switch (variant) {
        case 'default':
            variantSpecificStyles = "bg-gray-900 text-white hover:bg-gray-800";
            break;
        case 'outline':
            variantSpecificStyles = "bg-white border border-gray-200 shadow-sm hover:bg-gray-50 hover:shadow-md text-gray-900";
            break;
        case 'ghost':
            variantSpecificStyles = "text-gray-700 hover:bg-gray-100 hover:shadow-sm";
            break;
    }

    let sizeSpecificStyles = "";
    if (size === 'icon') {
        // For icon buttons, padding is often minimal or zero, size is controlled by h/w classes
        sizeSpecificStyles = "p-0";
    }
    // Note: specific dimensions like h-10, w-[150px], h-8, w-8 are expected to be in the className prop from month-picker.tsx

    return (
        <button
            type={type}
            className={cn(
                baseStyles,
                variantSpecificStyles,
                sizeSpecificStyles,
                className // Crucial for allowing overrides and specific styling from the call site
            )}
            {...props}
        >
            {children}
        </button>
    );
};

export { CustomButton }; 