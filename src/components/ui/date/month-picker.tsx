"use client"

import { useState } from "react"
import { ChevronLeft, ChevronRight, Calendar } from "lucide-react"
import { CustomButton } from "@/components/ui/button/custom-button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { But<PERSON> } from "../button"

const months = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]

export interface MonthPickerProps {
    onDateChange?: (year: number, month: number) => void;
}

export default function MonthPicker({ onDateChange }: MonthPickerProps) {
    const currentDate = new Date()
    const [selectedYear, setSelectedYear] = useState(currentDate.getFullYear())
    const [selectedMonth, setSelectedMonth] = useState(currentDate.getMonth())
    const [open, setOpen] = useState(false)

    const handleMonthSelect = (monthIndex: number) => {
        setSelectedMonth(monthIndex)
        setOpen(false)
        onDateChange?.(selectedYear, monthIndex + 1); // 月份从1开始
    }

    const handlePreviousYear = () => {
        const newYear = selectedYear - 1;
        setSelectedYear(newYear)
        onDateChange?.(newYear, selectedMonth + 1); // 月份从1开始
    }

    const handleNextYear = () => {
        const newYear = selectedYear + 1;
        setSelectedYear(newYear)
        onDateChange?.(newYear, selectedMonth + 1); // 月份从1开始
    }

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    className="w-[140px] h-10 justify-between text-sm font-medium bg-white border-gray-200 shadow-sm hover:shadow-md hover:bg-gray-50 focus:ring-2 focus:ring-gray-300 focus:border-gray-300 transition-all duration-200 rounded-lg"
                >
                    <Calendar className="h-3 w-3 text-gray-500" />
                    <span className="text-gray-600 text-xs">
                        {selectedYear}年 {months[selectedMonth]}
                    </span>
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[280px] p-0 bg-white border-gray-200 shadow-xl rounded-xl" align="start">
                {/* 年份选择器 */}
                <div className="p-4 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                        <CustomButton
                            variant="ghost"
                            size="icon"
                            onClick={handlePreviousYear}
                            className="h-8 w-8 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-gray-300 transition-all duration-200"
                        >
                            <ChevronLeft className="h-4 w-4 text-gray-600" />
                        </CustomButton>
                        <div className="text-sm font-semibold text-gray-900 bg-gray-50 px-3 py-1 rounded-lg">{selectedYear}年</div>
                        <CustomButton
                            variant="ghost"
                            size="icon"
                            onClick={handleNextYear}
                            className="h-8 w-8 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-gray-300 transition-all duration-200"
                        >
                            <ChevronRight className="h-4 w-4 text-gray-600" />
                        </CustomButton>
                    </div>
                </div>

                {/* 月份网格 */}
                <div className="grid grid-cols-3 gap-2 p-4">
                    {months.map((month, index) => (
                        <CustomButton
                            key={month}
                            variant={selectedMonth === index ? "default" : "ghost"}
                            className={`
                h-10 text-sm font-medium rounded-lg transition-all duration-200
                ${selectedMonth === index
                                    ? "bg-gray-900 text-white shadow-lg ring-2 ring-gray-900/20 text-xs"
                                    : "text-gray-700 hover:bg-gray-100 hover:shadow-sm focus:ring-2 focus:ring-gray-300 text-xs"
                                }
              `}
                            onClick={() => handleMonthSelect(index)}
                        >
                            {month}
                        </CustomButton>
                    ))}
                </div>
            </PopoverContent>
        </Popover>
    )
}
